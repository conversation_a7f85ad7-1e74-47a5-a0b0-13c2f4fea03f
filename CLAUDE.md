# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Gradle Commands
```bash
# Build the project
./gradlew build

# Run tests
./gradlew test

# Start application in debug mode (port 5005)
./gradlew bootRun -Pdebug

# Clean build
./gradlew clean build
```

### Docker Development
```bash
# Start local development environment
./manage.sh start

# Stop containers
./manage.sh stop

# List running containers  
./manage.sh --list

# Access Java container shell
./manage.sh exec java

# Quick development run (builds continuously + debug mode)
./run.sh
```

## Architecture Overview

This is an AI-powered sports article generation service that automatically creates articles for scheduled matches using LLM services and sports data APIs.

### Core Domain Flow
1. **Schedule Management**: Users create schedules for upcoming matches with templates
2. **Data Enrichment**: System fetches comprehensive sports data from multiple Sportal365 APIs  
3. **Content Generation**: LLM services generate articles using enriched match data and templates
4. **Publication**: Generated articles with dynamic widgets are posted to content management system

### Key Domain Models

- **Schedule**: Central entity managing article generation tasks with match details, templates, and status
- **Template**: Defines article structure with prompt templates and widget sections for LLM generation
- **MatchDetails**: Rich sports data model containing teams, statistics, lineups, standings
- **Widget**: Reusable sports content components (standings, player stats, team comparisons)

### Service Architecture

**Application Layer** (`application/service/`):
- `ScheduleService`: Schedule lifecycle management
- `ArticleGenerationService`: Main generation pipeline orchestration
- `TemplateGenerationService`: Builds LLM prompts from templates and match data
- `LlmService`: AI model integration for content generation
- `ContentService`: Article publishing to CMS
- `EditorBlockService`: Structured content assembly

**Domain Services** (`domain/`):
- `MatchDetailsService`: Sports data enrichment pipeline
- `WidgetGenerationService`: Dynamic sports widget creation

**External Integrations** (`infrastructure/client/`):
- Sportal365 APIs: Football, FormGuide, SportSearch, Standings, Statistics
- LLM Client: AI content generation
- Content API: Article publishing with authentication

### Data Processing Pipeline

The system uses a pipeline pattern for match data enrichment:
1. Base match details from Football API
2. Team statistics and form guide data
3. Standings and competition context
4. Player lineups and squad information
5. Template processing with enriched data
6. LLM generation and widget assembly

### Configuration

Environment variables in `docker-compose-local.yaml` control:
- MongoDB connection and database selection
- Scheduler cron expressions for batch processing
- External API endpoints and credentials
- RabbitMQ for configuration updates

### Testing Strategy

- Unit tests use JUnit 5 platform
- Integration tests use Spring Boot test framework
- Sports data APIs are tested with mock responses
- LLM integration uses test doubles for deterministic results