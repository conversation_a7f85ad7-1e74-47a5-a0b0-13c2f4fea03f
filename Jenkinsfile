@Library(['common']) _

pipeline {
    agent any

    options {
        disableConcurrentBuilds(abortPrevious: true)
    }

    environment {
        SONAR_HOST_URL = 'https://sonarqube.internal.sportal365.com'
        MASTER_BRANCH_NAME = 'master'
        JAVA_CONTAINER_NAME = "eu.gcr.io/mythic-producer-212107/${PROJECT_NAME}-java"
        NGINX_CONTAINER_NAME = "eu.gcr.io/mythic-producer-212107/${PROJECT_NAME}-web"
        K8S_TAG_PRERELEASE = 'prerelease'
        K8S_TAG_RELEASE = 'release'
        PROJECT_NAME = "${env.JOB_NAME.split('/')[0]}"
        DOCKER_TAG_NAME = "${env.GIT_BRANCH}-${env.BUILD_NUMBER}"
        SHOULD_BUILD_NGINX_IMAGE = "true"
        TAG_NAME = "${env.GIT_BRANCH}".replaceAll('refs/tags/', '')
        JDK_VERSION = '17'
    }

    stages {
        stage('Initialize') {
            steps {
                initializeStage()
            }
        }

        stage('Checkout and Setup') {
            steps {
                retry(5) {
                    script {
                        if (checkAborted()) error("Build aborted")
                        try {
                            sh '''
                                apt-get update && apt-get install -y wget
                                wget https://github.com/earthly/earthly/releases/download/v0.8.13/earthly-linux-amd64 -O /usr/local/bin/earthly
                                chmod +x /usr/local/bin/earthly
                                /usr/local/bin/earthly bootstrap --with-autocomplete
                                <NAME_EMAIL>:sportal-media-platform/common-earthly-config.git /tmp/common-earthly-config
                                cp /tmp/common-earthly-config/java/Earthfile $WORKSPACE/Earthfile
                            '''
                            def gitConfigContent = sh(script: 'cat /root/.gitconfig', returnStdout: true).trim()
                            env.GIT_CONFIG_CONTENT = gitConfigContent
                        } catch (Exception e) {
                            sleep(time: 10, unit: "SECONDS")
                            error("Checkout and Setup failed: ${e.message}")
                        }
                    }
                }
            }
        }

        stage('Run Earthly Pipeline') {
            steps {
                retry(5) {
                    script {
                        if (checkAborted()) error("Build aborted")
                        try {
                            withCredentials([
                                usernamePassword(credentialsId: 'sonarqube-creds', usernameVariable: 'SONAR_LOGIN', passwordVariable: 'SONAR_PASSWORD')
                            ]) {
                                docker.withRegistry('https://eu.gcr.io', 'gcr:mythic-producer-212107') {
                                    sh '''
                                        eval "\$(ssh-agent -s)"
                                        ssh-add /root/.ssh/id_rsa
                                        export SSH_AUTH_SOCK=\$(find /tmp/ssh-*/agent.* | head -n 1)
                                        earthly --disable-remote-registry-proxy --allow-privileged --ssh-auth-sock="$SSH_AUTH_SOCK" --push +pipeline \
                                            --SONAR_HOST_URL="$SONAR_HOST_URL" \
                                            --MASTER_BRANCH_NAME="$MASTER_BRANCH_NAME" \
                                            --PROJECT_NAME="$PROJECT_NAME" \
                                            --JAVA_CONTAINER_NAME="$JAVA_CONTAINER_NAME" \
                                            --NGINX_CONTAINER_NAME="$NGINX_CONTAINER_NAME" \
                                            --K8S_TAG_PRERELEASE="$K8S_TAG_PRERELEASE" \
                                            --K8S_TAG_RELEASE="$K8S_TAG_RELEASE" \
                                            --DOCKER_TAG_NAME="$DOCKER_TAG_NAME" \
                                            --SHOULD_BUILD_NGINX_IMAGE="$SHOULD_BUILD_NGINX_IMAGE" \
                                            --TAG_NAME="$TAG_NAME" \
                                            --SONAR_LOGIN="$SONAR_LOGIN" \
                                            --SONAR_PASSWORD="$SONAR_PASSWORD" \
                                            --CHANGE_ID="$CHANGE_ID" \
                                            --BRANCH_NAME="$BRANCH_NAME" \
                                            --GIT_REPO="*****************:sportal-media-platform" \
                                            --GIT_CONFIG_CONTENT="$GIT_CONFIG_CONTENT" \
                                            --JDK_VERSION="$JDK_VERSION"
                                    '''
                                }
                            }
                        } catch (Exception e) {
                            sleep(time: 10, unit: "SECONDS")
                            error("Earthly pipeline failed: ${e.message}")
                        }
                    }
                }
            }
        }
    }

    post {
        success {
            script {
                if (env.TAG_NAME.startsWith('prerelease')) {
                    echo "Triggering MultiSportApi-Tests job with COMMIT_HASH: ${env.GIT_COMMIT}"
                    build job: 'Tests By API/MultiSportApi-Tests', parameters: [
                        string(name: 'COMMIT_HASH', value: env.GIT_COMMIT)
                    ], wait: false
                } else {
                    echo "Current tag '${env.TAG_NAME}' is not staging tag. Skipping test job trigger."
                }
            }
        }
        always {
            cleanWorkspacePost()
        }
    }
}