options:
  docker: true

definitions:
  services:
    docker:
      image: docker:dind
      memory: 16384

  step_defaults: &step-defaults
    size: 8x
    runs-on:
      - self.hosted
      - linux
    services:
      - docker

  scripts:
    export-common-vars: &export-common-vars |
      export PROJECT_NAME="${BITBUCKET_REPO_SLUG}"
      export JAVA_CONTAINER_NAME="eu.gcr.io/mythic-producer-212107/${PROJECT_NAME}-java"
      export NGINX_CONTAINER_NAME="eu.gcr.io/mythic-producer-212107/${PROJECT_NAME}-web"
      export CHANGE_ID="${BITBUCKET_PR_ID:-}"

    install-packages: &install-packages |
      apt-get update && apt-get install -y curl sudo git openssh-client

    setup-ssh-agent: &setup-ssh-agent |
      mkdir -p ~/.ssh
      echo "StrictHostKeyChecking no" >> ~/.ssh/config
      chmod 600 ~/.ssh/config
      (umask 077 && echo $SSH_PRIVATE_KEY | base64 --decode > ~/.ssh/id_rsa)
      chmod 600 ~/.ssh/id_rsa
      echo "$SSH_PUBLIC_KEY" > ~/.ssh/id_rsa.pub
      chmod 644 ~/.ssh/id_rsa.pub
      eval $(ssh-agent -s)
      ssh-add ~/.ssh/id_rsa
      ssh-keyscan bitbucket.org >> ~/.ssh/known_hosts
      ssh-add -l

    install-dagger: &install-dagger |
      curl -L https://dl.dagger.io/dagger/install.sh | DAGGER_VERSION="${DAGGER_VERSION}" bash -s --
      sudo mv bin/dagger /usr/local/bin/dagger

    configure-git: &configure-git |
      git config --global user.email "<EMAIL>"
      git config --global user.name "CI User"
      export GIT_CONFIG_CONTENT="$(cat ~/.gitconfig || echo '')"

    configure-docker-gcr: &configure-docker-gcr |
      mkdir -p /root/.docker
      echo "$GCR_DOCKER_CONFIG" | base64 -d > /root/.docker/config.json

    login-docker-hub: &login-docker-hub |
      docker login -u "$DOCKERHUB_USERNAME" --password-stdin <<< "$DOCKERHUB_PASSWORD"

    export-ssh-key-base64: &export-ssh-key-base64 |
      export SSH_PRIVATE_KEY_BASE64="$(cat ~/.ssh/id_rsa | base64 -w 0)"

pipelines:
  default:
    - step:
        <<: *step-defaults
        name: "Build/Test/Scan Only"
        deployment: test
        # Reference caches to persist between jobs
        # caches:
        #   - docker
          
        script:
          - set -euo pipefail
          - *export-common-vars
          - export TAG_NAME="${BITBUCKET_TAG:-}"
          - export DOCKER_TAG_NAME="${BITBUCKET_BRANCH}-${BITBUCKET_BUILD_NUMBER}"
          - *install-packages
          - *setup-ssh-agent
          - *install-dagger
          - *configure-git
          - *configure-docker-gcr
          - *login-docker-hub
          - dagger -m "*****************:sportal-media-platform/dagger-pipelines.git/java@master" call build-only --src=. --jdkVersion="$JDK_VERSION" --projectName="$PROJECT_NAME" --branchName="$BITBUCKET_BRANCH" --masterBranchName="$MASTER_BRANCH_NAME" --changeId="$CHANGE_ID" --sonarHostUrl="$SONAR_HOST_URL" --sonarToken="$SONAR_TOKEN" -vvv --debug

  branches:
    master:
      - step:
          <<: *step-defaults
          name: "Deploy to Integration"
          deployment: integration
          # caches:
          #   - docker
          script:
            - set -euo pipefail
            - *export-common-vars
            - export TAG_NAME="${BITBUCKET_TAG:-}"
            - export DOCKER_TAG_NAME="${BITBUCKET_BRANCH}-${BITBUCKET_BUILD_NUMBER}"
            - *install-packages
            - *setup-ssh-agent
            - *install-dagger
            - *configure-git
            - *configure-docker-gcr
            - *login-docker-hub
            - *export-ssh-key-base64
            - dagger -m "*****************:sportal-media-platform/dagger-pipelines.git/java@master" call deploy-integration --src=. --projectName="$PROJECT_NAME" --jdkVersion="$JDK_VERSION" --branchName="$BITBUCKET_BRANCH" --masterBranchName="$MASTER_BRANCH_NAME" --changeId="$CHANGE_ID" --javaContainerName="$JAVA_CONTAINER_NAME" --nginxContainerName="$NGINX_CONTAINER_NAME" --dockerTagName="$DOCKER_TAG_NAME" --shouldBuildNginxImage="$SHOULD_BUILD_NGINX_IMAGE" --sonarHostUrl="$SONAR_HOST_URL" --sonarToken="$SONAR_TOKEN" --gitConfigContent="$GIT_CONFIG_CONTENT" --gitRepoBase="$GIT_REPO_BASE" --devDeployRepoName="$DEV_DEPLOY_REPO_NAME" --sshPrivateKeyBase64="$SSH_PRIVATE_KEY_BASE64" -vvv --debug

    main:
      - step:
          <<: *step-defaults
          name: "Deploy to Integration"
          deployment: integration
          # caches:
          #   - docker
          script:
            - set -euo pipefail
            - *export-common-vars
            - export MASTER_BRANCH_NAME="main"
            - export TAG_NAME="${BITBUCKET_TAG:-}"
            - export DOCKER_TAG_NAME="${BITBUCKET_BRANCH}-${BITBUCKET_BUILD_NUMBER}"
            - *install-packages
            - *setup-ssh-agent
            - *install-dagger
            - *configure-git
            - *configure-docker-gcr
            - *login-docker-hub
            - *export-ssh-key-base64
            - dagger -m "*****************:sportal-media-platform/dagger-pipelines.git/java@master" call deploy-integration --src=. --projectName="$PROJECT_NAME" --jdkVersion="$JDK_VERSION" --branchName="$BITBUCKET_BRANCH" --masterBranchName="$MASTER_BRANCH_NAME" --changeId="$CHANGE_ID" --javaContainerName="$JAVA_CONTAINER_NAME" --nginxContainerName="$NGINX_CONTAINER_NAME" --dockerTagName="$DOCKER_TAG_NAME" --shouldBuildNginxImage="$SHOULD_BUILD_NGINX_IMAGE" --sonarHostUrl="$SONAR_HOST_URL" --sonarToken="$SONAR_TOKEN" --gitConfigContent="$GIT_CONFIG_CONTENT" --gitRepoBase="$GIT_REPO_BASE" --devDeployRepoName="$DEV_DEPLOY_REPO_NAME" --sshPrivateKeyBase64="$SSH_PRIVATE_KEY_BASE64" -vvv --debug

  tags:
    "prerelease-*":
      - step:
          <<: *step-defaults
          name: "Deploy to Staging"
          deployment: staging
          # caches:
          #   - docker
          script:
            - set -euo pipefail
            - *export-common-vars
            - export TAG_NAME="${BITBUCKET_TAG}"
            - export DOCKER_TAG_NAME="${BITBUCKET_TAG}-${BITBUCKET_BUILD_NUMBER}"
            - export JENKINS_JOB_PATH="${JENKINS_JOB_PATH:-}"
            - *install-packages
            - *setup-ssh-agent
            - *install-dagger
            - *configure-git
            - *configure-docker-gcr
            - *login-docker-hub
            - *export-ssh-key-base64
            - dagger -m "*****************:sportal-media-platform/dagger-pipelines.git/java@master" call deploy-staging --src=. --projectName="$PROJECT_NAME" --javaContainerName="$JAVA_CONTAINER_NAME" --nginxContainerName="$NGINX_CONTAINER_NAME" --dockerTagName="$DOCKER_TAG_NAME" --branchName="$TAG_NAME" --changeId="$CHANGE_ID" --masterBranchName="$MASTER_BRANCH_NAME" --shouldBuildNginxImage="$SHOULD_BUILD_NGINX_IMAGE" --jdkVersion="$JDK_VERSION" --sonarHostUrl="$SONAR_HOST_URL" --sonarToken="$SONAR_TOKEN" --gitConfigContent="$GIT_CONFIG_CONTENT" --gitRepoBase="$GIT_REPO_BASE" --devDeployRepoName="$DEV_DEPLOY_REPO_NAME" --sshPrivateKeyBase64="$SSH_PRIVATE_KEY_BASE64" --jenkinsUrl="$JENKINS_URL" --jenkinsJobPath="$JENKINS_JOB_PATH" --jenkinsUser="$JENKINS_USER" --jenkinsToken="$JENKINS_TOKEN" --bitbucketCommit="$BITBUCKET_COMMIT" --jenkinsCheckoutBranch="$JENKINS_CHECKOUT_BRANCH" -vvv --debug
# Has to be defined on staging deployment level in bitbucket
# JENKINS_JOB_PATH=job/Tests%20By%20API/job/BasketballApi-Tests/buildWithParameters?COMMIT_HASH=${BITBUCKET_COMMIT}&EXECUTION_ENVIRONMENT=staging&TESTS_FILTER=&EXECUTE_TESTS=true&SLACK_CHANNEL_NOTIFICATION=%23platform-reports&CLAUDE_ANALYSIS=false&BRANCH=${JENKINS_CHECKOUT_BRANCH}&TESTS_GROUP=%26basketballApi
    "release-*":
      - step:
          <<: *step-defaults
          name: "Deploy to Production"
          deployment: production
          # caches:
          #   - docker
          script:
            - set -euo pipefail
            - *export-common-vars
            - export TAG_NAME="${BITBUCKET_TAG}"
            - export DOCKER_TAG_NAME="${BITBUCKET_TAG}-${BITBUCKET_BUILD_NUMBER}"
            - *install-packages
            - *setup-ssh-agent
            - *install-dagger
            - *configure-git
            - *configure-docker-gcr
            - *login-docker-hub
            - *export-ssh-key-base64
            - dagger -m "*****************:sportal-media-platform/dagger-pipelines.git/java@master" call deploy-production --src=. --projectName="$PROJECT_NAME" --javaContainerName="$JAVA_CONTAINER_NAME" --nginxContainerName="$NGINX_CONTAINER_NAME" --dockerTagName="$DOCKER_TAG_NAME" --branchName="$TAG_NAME" --changeId="$CHANGE_ID" --masterBranchName="$MASTER_BRANCH_NAME" --shouldBuildNginxImage="$SHOULD_BUILD_NGINX_IMAGE" --jdkVersion="$JDK_VERSION" --sonarHostUrl="$SONAR_HOST_URL" --sonarToken="$SONAR_TOKEN" --gitConfigContent="$GIT_CONFIG_CONTENT" --gitRepoBase="$GIT_REPO_BASE" --prodDeployRepoName="$PROD_DEPLOY_REPO_NAME" --sshPrivateKeyBase64="$SSH_PRIVATE_KEY_BASE64" -vvv --debug