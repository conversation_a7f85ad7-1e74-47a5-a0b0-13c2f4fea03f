# Article Scheduler Service - Documentation Update

## 🆕 New Feature: Schedule Type Functionality (PLT-662)

### Overview
The Article Scheduler Service now supports immediate vs scheduled article generation through the new `schedule_type` field, enabling frontend toggle switch functionality for enhanced user control.

---

## 📋 Updated API Documentation

### POST /schedules

Creates new article generation schedules with support for immediate or scheduled processing.

#### Request Headers
- `Content-Type: application/json`
- `X-Project: {project_domain}` (required)
- `Authorization: Basic {credentials}` (required)

#### Updated Request Body Schema

```json
{
    "template_type": "PRE_GAME",
    "template_name": "Match Preview team news and quotes",
    "time_zone": "Europe/Sofia",
    "generate_summary": true,
    "generate_strapline": true,
    "category_id": "2025011511551414715",
    "category_name": "test-propертъ",
    "user_id": "2018060808173367773",
    "user_name": "Service Admin",
    "sport": "FOOTBALL",
    "schedule_type": "immediately",  // 🆕 NEW OPTIONAL FIELD
    "matches": [
        {
            "match_id": "c9ad762a-09da-4a40-89b3-f8a5483cb293",
            "competition_id": "92c3e74c-665b-4251-8ddb-d3bb042140f3",
            "competition_name": "Световно клубно първенство",
            "match_name": "Челси - ФК Лос Анджелис",
            "match_date": "2025-06-18T19:00:00.000Z"
        }
    ]
}
```

---

## 🆕 New Field: schedule_type

| Field | Type | Required | Values | Default | Description |
|-------|------|----------|--------|---------|-------------|
| `schedule_type` | string | **No** | `"immediately"`, `"scheduled"` | `"immediately"` | Determines when article generation occurs |

### Schedule Type Behavior Matrix

| Value | Processing Behavior | Frontend Toggle | Use Case | Performance |
|-------|-------------------|-----------------|----------|-------------|
| `"immediately"` | All schedules processed immediately upon creation | "Generate Now" | Real-time article generation | Instant processing |
| `"scheduled"` | Uses existing time-based scheduling logic | "Schedule for Later" | Traditional scheduling | Queue-based processing |
| *omitted* | **Defaults to immediate processing** | Default behavior | Backward compatibility | Instant processing |

---

## 📝 API Request Examples

### 1. Immediate Processing (New Default)
```bash
curl --location 'localhost:8080/schedules' \
--header 'Content-Type: application/json' \
--header 'X-Project: sportal.bg' \
--header 'Authorization: Basic {credentials}' \
--data '{
    "template_type": "PRE_GAME",
    "template_name": "Match Preview team news and quotes",
    "time_zone": "Europe/Sofia",
    "generate_summary": true,
    "generate_strapline": true,
    "category_id": "2025011511551414715",
    "category_name": "test-propертъ",
    "user_id": "2018060808173367773",
    "user_name": "Service Admin",
    "sport": "FOOTBALL",
    "schedule_type": "immediately",
    "matches": [
        {
            "match_id": "c9ad762a-09da-4a40-89b3-f8a5483cb293",
            "competition_id": "92c3e74c-665b-4251-8ddb-d3bb042140f3",
            "competition_name": "Световно клубно първенство",
            "match_name": "Челси - ФК Лос Анджелис",
            "match_date": "2025-06-18T19:00:00.000Z"
        }
    ]
}'
```

### 2. Scheduled Processing
```bash
curl --location 'localhost:8080/schedules' \
--header 'Content-Type: application/json' \
--header 'X-Project: sportal.bg' \
--header 'Authorization: Basic {credentials}' \
--data '{
    "template_type": "PRE_GAME",
    "template_name": "Match Preview team news and quotes",
    "sport": "FOOTBALL",
    "schedule_type": "scheduled",
    "matches": [...]
}'
```

### 3. Backward Compatible (No schedule_type)
```bash
curl --location 'localhost:8080/schedules' \
--header 'Content-Type: application/json' \
--header 'X-Project: sportal.bg' \
--header 'Authorization: Basic {credentials}' \
--data '{
    "template_type": "PRE_GAME",
    "template_name": "Match Preview team news and quotes",
    "sport": "FOOTBALL",
    "matches": [...]
    // No schedule_type field - defaults to "immediately"
}'
```

---

## ✅ Validation Rules

| Rule | Description | Example |
|------|-------------|---------|
| **Optional Field** | `schedule_type` can be omitted from requests | ✅ Valid |
| **Valid Values** | Only accepts `"immediately"` or `"scheduled"` | ✅ Valid |
| **Case Sensitivity** | Accepts both lowercase and uppercase | `"IMMEDIATELY"` ✅ Valid |
| **Invalid Values** | Rejects any other values | `"invalid"` ❌ Error |
| **Default Behavior** | When omitted, defaults to immediate processing | ✅ Backward compatible |

### Error Response for Invalid Values
```json
{
    "error": "Validation failed: schedule_type must be 'immediately' or 'scheduled' when provided"
}
```

---

## 🔄 Backward Compatibility

### ✅ **Fully Backward Compatible**
- **Existing API consumers** continue to work without any changes
- **No breaking changes** to current functionality
- **Default behavior change**: When `schedule_type` is omitted, now defaults to immediate processing
- **Response format** remains unchanged

### Migration Guide
- **No action required** for existing integrations
- **Optional enhancement**: Add `schedule_type: "scheduled"` to maintain previous behavior
- **Recommended**: Use `schedule_type: "immediately"` for real-time processing

---

## 🎛️ Frontend Integration

### Toggle Switch Component Support
The new `schedule_type` field enables frontend toggle switch functionality:

| Frontend Toggle State | API Request Value | Backend Behavior |
|----------------------|-------------------|------------------|
| **"Generate Now"** | `"schedule_type": "immediately"` | Immediate article generation |
| **"Schedule for Later"** | `"schedule_type": "scheduled"` | Time-based scheduling |
| **Default (No Selection)** | *field omitted* | Immediate processing |

---

## 📊 Performance Impact

### Processing Comparison

| Schedule Type | Processing Time | Queue Usage | Resource Impact |
|---------------|----------------|-------------|-----------------|
| `immediately` | **Instant** | Bypassed | Higher immediate load |
| `scheduled` | Based on timing rules | Standard queue | Distributed load |

---

## 🧪 Testing

### Unit Test Coverage
- **11 comprehensive unit tests** covering all scenarios
- **100% coverage** of `schedule_type` functionality
- **Real-world API compatibility** testing
- **Validation logic** testing
- **Edge cases** and error handling

### Test Files
- `ScheduleServiceTest.java` - Core functionality tests
- `SimpleScheduleServiceTest.java` - Comprehensive coverage tests

---

## 📚 Related Documentation

- [Schedule Endpoint Unit Tests](link-to-subpage) - Detailed test documentation
- [PLT-662](https://jira.company.com/browse/PLT-662) - Original requirement ticket
- [Frontend Toggle Component](#) - Frontend implementation docs *(coming soon)*

---

## 🔗 Quick Links

- **Main API Endpoint**: `POST /schedules`
- **New Field**: `schedule_type` (optional)
- **Valid Values**: `"immediately"`, `"scheduled"`
- **Default**: `"immediately"`
- **Backward Compatible**: ✅ Yes

---

*Last Updated: [Current Date] - PLT-662 Implementation*
