map $http_origin $cors_header {
    default '*';
    "~*^http" "$http_origin";
}

server {
        listen 80;
        listen [::]:80;

        client_body_in_file_only clean;
        client_body_buffer_size 32K;

        client_max_body_size 20M;

        sendfile on;
        send_timeout 300s;

        error_log  /var/log/nginx/error.log;
        access_log /var/log/nginx/access.log;

        location /actuator/health {
            proxy_pass http://#JAVA_CONTAINER_NAME#:8080;
            proxy_pass_request_headers on;
        }

        location /actuator {
            deny all;
        }

        location / {
            proxy_pass http://#JAVA_CONTAINER_NAME#:8080;
            proxy_pass_request_headers on;
        }
}
