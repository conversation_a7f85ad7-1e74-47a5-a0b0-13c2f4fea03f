version: '3.5'
services:
  java:
    image: openjdk:17-jdk-slim-buster
    volumes:
      - ./:/article-generation-service
      - $GRADLE_DIR:/root/.gradle
      - java_gradle:/article-generation-service/.gradle
      - java_build:/article-generation-service/build
    extra_hosts:
      - "host.docker.internal:host-gateway"
    working_dir: /article-generation-service
    command: sh run.sh
    restart: always
    environment:
      - MONGO_DB_URI=mongodb://root:<EMAIL>:27017,mongodb-cluster-percona-crd-rs0-1.mongodb-cluster-percona-crd-rs0.services-integration.svc.cluster.local:27017,mongodb-cluster-percona-crd-rs0-2.mongodb-cluster-percona-crd-rs0.services-integration.svc.cluster.local:27017/
      - MONGO_DB_NAME=article-generation-service-integration
      - SCHEDULER_CRON_EXPRESSION=0 0 0 * * *
      - CLIENT_API_BASE_URL=client-api-svc.integration
      - CLIENT_API_USERNAME=smp-admin
      - CLIENT_API_PASSWORD=1Ox1Fd03dHIFlg5B
      - RABBITMQ_HOST=rabbitmq-integration-app.services-integration
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=guest
      - RABBITMQ_PASSWORD=guest
      - RABBITMQ_CONFIG_CLIENT_QUEUE=scheduler-api-sport-project-events
      - RABBITMQ_CONFIG_CLIENT_VHOST=/project-provisioning-localdev
      - RABBITMQ_CONFIG_CLIENT_EXCHANGE=sports-data-project-events
      - RABBITMQ_CONFIG_CLIENT_PROJECT_QUEUE=scheduler-api-project-events
      - RABBITMQ_CONFIG_CLIENT_PROJECT_EXCHANGE=amq.topic
      - RABBITMQ_CONFIG_CLIENT_PROJECT_ROUTING_KEY=PROVISIONING_SERVICE
      - MIGRATION_SCHEDULE_IDS_ENABLED=true
      - DEFAULT_TEMPLATE_PROJECT=default-template-project
      - FOOTBALL_API_BASE_URL=football-api-svc.integration
      - FOOTBALL_API_USERNAME=sportal365
      - FOOTBALL_API_PASSWORD=zxTHFqTOP73fm9TKkukzvXhClH05tsUf
      - LLM_SERVICE=llm-integration-service-svc.integration
      - SEARCH_API_BASE_URL=search-api-v2-svc.integration
      - STANDINGS_API_BASE_URL=standing-api-svc.integration
      - STANDINGS_API_USERNAME=sportal365
      - STANDINGS_API_PASSWORD=zxTHFqTOP73fm9TKkukzvXhClH05tsUf
      - FORM_GUIDE_API_BASE_URL=form-guide-api-svc.integration
      - STATISTICS_API_BASE_URL=sports-statistics-api-svc.integration
      - CONTENT_API_BASE_URL=content-api-svc.integration
      - CONTENT_API_USERNAME=<EMAIL>
      - CONTENT_API_PASSWORD=ServiceAdmin@SMP8
      - CONTENT_API_CLIENT_ID=2
      - CONTENT_API_CLIENT_SECRET=bpDHktFkIT9eEHdpIr2xOR4s4pKWIKcEFF9LNr26
      - WIDGET_BLOCKY_API_BASE_URL=widgets-blocky-service-svc.integration
      #- SENTRY_DSN=https://<EMAIL>/4508793584877568
      #- SENTRY_SAMPLE_RATE=1.0
      #- SENTRY_ENV=local
    ports:
      - "5005:5005"

  web:
    build:
      context: .
      dockerfile: Dockerfile-local.nginx
    ports:
      - '8080:8080'
    environment:
      - JAVA_CONTAINER_NAME=java

volumes:
  java_gradle:
  java_build:

