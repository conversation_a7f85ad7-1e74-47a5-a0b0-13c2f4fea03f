package com.sportal365.articlescheduler.application.service.debug;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.template.model.PromptExample;
import com.sportal365.articlescheduler.infrastructure.persistence.entity.TemplateDocument;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.time.Instant;

/**
 * Service responsible for saving filled prompt templates to MongoDB for debugging purposes.
 * This service stores template examples in the database solely for debugging and troubleshooting
 * article generation issues. The saved templates can be accessed through debug endpoints
 * in production environments to analyze prompt structure and parameters used during generation.
 *
 * Key responsibilities:
 * - Converting filled prompt templates to JSON format
 * - Creating prompt example records with match context
 * - Storing examples in the prompt_examples collection for debugging analysis
 * - Preserving template state and match details for troubleshooting
 *
 * Note: The saved examples serve no purpose other than debugging and are not used
 * for training, analytics, or any other operational functionality.
 */
@Service
@RequiredArgsConstructor
public class SaveTemplateToMongoService {

    private final ObjectMapper objectMapper;

    private final MongoTemplate mongoTemplate;

    /**
     * Saves a filled prompt template to the database for debugging purposes only.
     * This method performs the following steps:
     * 1. Converts the filled prompt template to JSON format for storage
     * 2. Creates a PromptExample record with template data and match context
     * 3. Stores the example in the prompt_examples collection for debugging analysis
     *
     * The saved template examples are used exclusively for debugging and troubleshooting
     * article generation issues. They can be accessed via debug endpoints in production
     * to analyze prompt structure, parameters, and match context during generation failures.
     *
     * @param promptTemplate the filled template document containing prompts and parameters
     * @param matchDetails the match information providing context for the template generation
     * @throws JsonProcessingException if there's an error converting the template to JSON format
     */
    public void save(TemplateDocument promptTemplate, MatchDetails matchDetails) throws JsonProcessingException {

        // 2) Convert the filled PromptTemplate to JSON
        String json = objectMapper.writeValueAsString(promptTemplate);

        // 3) Create a new PromptExample record
        PromptExample example = new PromptExample();
        example.setFilledPromptTemplate(json);
        example.setCurrentDate(Instant.now());
        example.setMatchName(matchDetails.getEventName());
        example.setDate(matchDetails.getDate());
        example.setTime(matchDetails.getTime());

        // 4) Save to Mongo (prompt_examples collection)
        PromptExample saved = mongoTemplate.save(example, "prompt_examples");
    }
}
