package com.sportal365.articlescheduler.application.util;


import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.enums.WidgetTypeEnum;

public class WidgetInputValidator {

    public static boolean isWidgetInputValid(WidgetTypeEnum widgetType, MatchDetails matchDetails) {
        if (WidgetTypeEnum.PLAYER_H2H.equals(widgetType)) {
            return isPlayerHeadToHeadInputValid(matchDetails);
        }

        return true;
    }

    private static boolean isPlayerHeadToHeadInputValid(MatchDetails matchDetails) {
        MatchDetails.ParticipantStatistics statistics = matchDetails.getStatistics();

        return statistics != null && statistics.getHomeTeam() != null && statistics.getAwayTeam() != null &&
                statistics.getHomeTeam().getStarPlayer() != null && statistics.getAwayTeam().getStarPlayer() != null;
    }

}
