package com.sportal365.articlescheduler.debug;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.bson.Document;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/debug")
@RequiredArgsConstructor
public class DebugController {

    private final DebugService debugService;

    // Endpoint: GET /rawData?collection={collectionName}&name={nameFilter}&date={dateFilter}
    @GetMapping(value = "/raw-data", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<?>> getRawData(
            @RequestParam(name="collection") String collection,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String date) {

        // Delegate to service to fetch documents
        List<Document> documents = debugService.fetchDocuments(collection, name, date);
        return ResponseEntity.ok(documents);
    }
}
