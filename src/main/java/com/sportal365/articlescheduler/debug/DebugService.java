package com.sportal365.articlescheduler.debug;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import java.util.List;
import org.bson.Document;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;

@Service
@AllArgsConstructor
public class DebugService {

    private final MongoTemplate mongoTemplate;

    /**
     * Fetches documents from the given collection, optionally filtered by name and/or date.
     */
    public List<Document> fetchDocuments(String collection, String name, String date) {
        // Build query criteria if filters are provided
        Query query = new Query();
        if (name != null && !name.isEmpty()) {
            query.addCriteria(Criteria.where("name").is(name));
        }
        if (date != null && !date.isEmpty()) {
            LocalDate localDate = LocalDate.parse(date);
            Instant startOfDay = localDate.atStartOfDay(ZoneOffset.UTC).toInstant();
            Instant endOfDay = localDate.plusDays(1).atStartOfDay(ZoneOffset.UTC).toInstant();
            query.addCriteria(Criteria.where("generation_time").gte(startOfDay).lt(endOfDay));
        }

        // Execute the query on the specified collection
        List<Document> results;
        if (query.getQueryObject().isEmpty()) {  // Use getQueryObject() instead
            results = mongoTemplate.findAll(Document.class, collection);
        } else {
            results = mongoTemplate.find(query, Document.class, collection);
        }

        // Convert ObjectId to string for each document
        for (Document doc : results) {
            if (doc.containsKey("_id")) {
                // Convert ObjectId to string representation
                doc.computeIfPresent("_id", (k, id) -> id.toString());
            }
        }
        return results;
    }
}
