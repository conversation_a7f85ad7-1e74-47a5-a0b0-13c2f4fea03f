package com.sportal365.articlescheduler.domain.calculator;

import com.sportal365.articlescheduler.application.dto.article.response.ArticleResponseDto;

public class ArticleCalculator {
    private static final double INPUT_PRICE_PER_TOKEN = 0.000003;      // $3/MTok = $0.000003 per token
    private static final double OUTPUT_PRICE_PER_TOKEN = 0.000015;     // $15/MTok = $0.000015 per token

    public static double calculateArticlePrice(ArticleResponseDto.Usage usage) {
        double promptCost = usage.promptTokens() * INPUT_PRICE_PER_TOKEN;
        double generationCost = usage.generationTokens() * OUTPUT_PRICE_PER_TOKEN;

        return promptCost + generationCost;
    }
}
