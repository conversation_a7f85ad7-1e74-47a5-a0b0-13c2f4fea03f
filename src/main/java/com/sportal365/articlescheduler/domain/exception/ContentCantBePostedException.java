package com.sportal365.articlescheduler.domain.exception;

import lombok.Getter;

@Getter
public class ContentCantBePostedException extends RuntimeException{
    private Integer statusCode;
    private String endpoint;
    private String requestDetails;

    public ContentCantBePostedException(String message) {
        super(message);
    }

    public ContentCantBePostedException(String message, Throwable cause) {
        super(message, cause);
    }

    public ContentCantBePostedException(String message, String endpoint, Integer statusCode) {
        super(message);
        this.endpoint = endpoint;
        this.statusCode = statusCode;
    }

    public ContentCantBePostedException(String message, String endpoint, String requestDetails, Integer statusCode) {
        super(message);
        this.endpoint = endpoint;
        this.requestDetails = requestDetails;
        this.statusCode = statusCode;
    }

}
