package com.sportal365.articlescheduler.domain.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.client.model.StageEvents;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder(toBuilder = true)
public class MatchDetails {

    private String matchId;
    private String legacyId;
    private String sport;
    private String country;
    private String tournamentName;
    private String tournamentId;
    private String season;
    private String seasonId;
    private String stageId;
    private String round;
    private String roundId;
    private String eventName;
    private String homeTeam;
    private String homeTeamId;
    private String homeTeamLegacyId;
    private String awayTeam;
    private String awayTeamId;
    private String awayTeamLegacyId;
    private String date;
    private String time;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String venue;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String referee;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Standings standings;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PlayoffsData playoffsData;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private RecentMatches recentMatches;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TeamNews teamNews;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Quotes quotes;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private LastMeeting lastMeeting;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<LastMeeting> lastMeetings;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Lineup lineup;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Squad squad;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ParticipantStatistics statistics;

    public List<String> getParticipantIds() {
        return List.of(homeTeamId, awayTeamId);
    }

    @Data
    @Builder
    public static class Standings {
        private TeamStandings homeTeam;
        private TeamStandings awayTeam;
        private boolean isPlayoffs;
        @Data
        @Builder
        public static class TeamStandings {
            private int position;
            private int points;
            private int gamesPlayed;
            private int goalsScored;
            private int goalsConceded;
            private String form;
        }
    }

    @Data
    @Builder
    public static class PlayoffsData {
        private List<StageEvents.Results> results;
    }

    @Data
    @Builder
    public static class RecentMatches {
        private List<MatchSummary> homeTeam;
        private List<MatchSummary> awayTeam;

        @Data
        @Builder
        public static class MatchSummary {
            private String opponent;
            private String result;
            private String score;
            private String date;
            private String competitionName;
        }
    }

    @Data
    @Builder
    public static class Lineup {
        private String status;
        private TeamDetails homeTeam;
        private TeamDetails awayTeam;

        @Data
        @Builder
        public static class TeamDetails {
            private String formation;
            private Coach coach;
            private List<Player> players;

            @Data
            @Builder
            public static class Coach {
                private String name;
            }

            @Data
            @Builder
            public static class Player {
                private Type type;
                private PlayerDetails player;
                private Integer shirtNumber;

                @Data
                @Builder
                public static class Type {
                    private String name;
                    private String category;
                }

                @Data
                @Builder
                public static class PlayerDetails {
                    private boolean active;
                    private String birthdate;
                    private String position;
                    private String name;

                    @Data
                    @Builder
                    public static class Country {
                        private String name;
                    }
                }
            }
        }
    }

    @Data
    @Builder
    public static class Squad {
        private Team homeTeam;
        private Team awayTeam;

        @Data
        @Builder
        public static class Team {
            private String id;
            private String name;
            private List<Player> players;
        }

        @Data
        @Builder
        public static class Player {
            private String id;
            private String name;
            private String position;
            private boolean active;
        }
    }

    @Data
    @Builder
    public static class TeamNews {
        private TeamInfo homeTeam;
        private TeamInfo awayTeam;

        @Data
        @Builder
        public static class TeamInfo {
            private List<String> injuries;
            private List<String> suspended;
            private List<String> keyPlayers;
            private List<String> returnees;
        }
    }

    @Data
    @Builder
    public static class Quotes {
        private ManagerQuote homeTeam;
        private TeamQuotes awayTeam;

        @Data
        @Builder
        public static class ManagerQuote {
            private String name;
            private String quote;
        }

        @Data
        @Builder
        public static class TeamQuotes {
            private ManagerQuote manager;
            private PlayerQuote player;

            @Data
            @Builder
            public static class PlayerQuote {
                private String name;
                private String quote;
            }
        }
    }

    @Data
    @Builder
    public static class LastMeeting {
        private String id;
        private String date;
        private String matchStatus;
        private String competitionName;
        private String competitionType;
        private String venue;
        private String finalResult;
        private String regularTimeResult;
        private String afterExtraTimeResult;
        private String penaltyResult;
        private String aggregateResult;
        private String roundName;
        private String roundType;
        private Scorers scorers;

        @Data
        @Builder
        public static class Scorers {
            private List<String> homeTeam;
            private List<String> awayTeam;
        }
    }

    @Data
    @Builder
    public static class ParticipantStatistics {
        private Team homeTeam;
        private Team awayTeam;

        @Data
        @Builder
        public static class Team {
            private String name;
            private Player starPlayer;
        }

        @Data
        @Builder
        public static class Player {
            private String id;
            private String name;
            private List<Statistic> averageSeasonStatistics;
            private List<EventStatistics> latestEventsStatistics;
            private List<EventStatistics> latestEventsStatisticsVersusOpponent;
        }

        @Data
        @Builder
        public static class EventStatistics {
            private String eventName;
            private List<Statistic> statistics;
        }

        @Data
        @Builder
        public static class Statistic {
            private String name;
            private String value;
        }
    }
}
