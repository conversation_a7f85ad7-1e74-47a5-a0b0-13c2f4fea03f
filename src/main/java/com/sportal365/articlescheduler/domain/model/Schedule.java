package com.sportal365.articlescheduler.domain.model;

import com.sportal365.articlescheduler.domain.model.enums.ScheduleStatus;
import com.sportal365.articlescheduler.domain.model.enums.ScheduleType;
import com.sportal365.common.enums.SportEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;

@Document(collection = "schedules")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@CompoundIndexes({
        @CompoundIndex(name = "project_status_time",
                def = "{'projectDomain': 1, 'status': 1, 'generationTime': 1}"),
        @CompoundIndex(name = "project_id",
                def = "{'projectDomain': 1, '_id': 1}", unique = true)
})
public class Schedule {
    @Id
    private String id;
    @Field("template_name")
    private String templateName;
    @Field("match_details")
    private MatchDetails matchDetails;
    @Field("generation_time")
    private Instant generationTime;
    @Builder.Default
    private ScheduleStatus status = ScheduleStatus.SCHEDULED;
    @Field("article_id")
    private String articleId;
    @Field("project_domain")
    private String projectDomain;
    @Field("created_at")
    @CreatedDate
    private Instant createdAt;
    @Field("updated_at")
    @LastModifiedDate
    private Instant updatedAt;
    @Field("time_zone")
    private String timeZone;
    @Field("generate_summary")
    private Boolean generateSummary;
    @Field("generate_strapline")
    private Boolean generateStrapline;
    private Category category;
    @Field("user_id")
    private String userId;
    @Field("user_name")
    private String userName;
    private SportEnum sport;
    @Field("price_for_generated_article")
    private Double priceForGeneratedArticle;
    @Field("retried_attempts")
    private int retriedAttempts;
    @Field("article_language")
    private String articleLanguage;
    @Field("llm_temperature")
    private String llmTemperature;
    @Field("provider_properties")
    private ProviderProperties providerProperties;
    private String language;
    @Field("schedule_type")
    private ScheduleType scheduleType;


    @Data
    @Builder
    public static class MatchDetails {
        @Field("match_id")
        private String matchId;
        @Field("match_name")
        private String matchName;
        @Field("competition_id")
        private String competitionId;
        @Field("competition_name")
        private String competitionName;
        @Field("match_date")
        private Instant matchDate;
    }

    @Data
    @Builder
    public static class Category {
        private String id;
        private String name;
    }

    @Data
    @Builder
    public static class ProviderProperties {
        private String provider;
        @Field("llm_model")
        private String llmModel;
    }
}
