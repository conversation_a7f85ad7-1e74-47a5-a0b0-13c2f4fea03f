package com.sportal365.articlescheduler.domain.model.enums;

public enum ScheduleStatus {
    SCHEDULED,
    COMPLETED,
    FAILED,
    INPROGRESS,
    RETRY;

    public static ScheduleStatus of(String status) {
        for (ScheduleStatus scheduleStatus : ScheduleStatus.values()) {
            if (scheduleStatus.name().equalsIgnoreCase(status)) {
                return scheduleStatus;
            }
        }
        return null;
    }
}
