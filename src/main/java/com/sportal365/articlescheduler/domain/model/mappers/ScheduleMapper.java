package com.sportal365.articlescheduler.domain.model.mappers;

import com.sportal365.articlescheduler.application.dto.schedule.response.ScheduleResponse;
import com.sportal365.articlescheduler.domain.model.Schedule;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

@Mapper(componentModel = "spring",  imports = {
        ZoneId.class,
        ZonedDateTime.class,
        LocalDateTime.class
})
public interface ScheduleMapper {


    @Mapping(target = "id", expression = "java(schedule.getId().toString())")
    @Mapping(target = "matchName", source = "matchDetails.matchName")
    @Mapping(target = "matchDate", expression = "java(schedule.getMatchDetails().getMatchDate().atZone(ZoneId.of(schedule.getTimeZone())))")
    @Mapping(target = "generationTime", expression = "java(schedule.getGenerationTime().atZone(ZoneId.of(schedule.getTimeZone())))")
    @Mapping(target = "competitionName", source = "matchDetails.competitionName")
    @Mapping(target = "categoryName", source = "category.name")
    @Mapping(target = "sport", source = "sport")
    @Mapping(target = "message", ignore = true)
    ScheduleResponse toResponse(Schedule schedule);

    List<ScheduleResponse> toResponseList(List<Schedule> schedules);

}
