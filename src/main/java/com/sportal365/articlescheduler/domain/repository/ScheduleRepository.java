package com.sportal365.articlescheduler.domain.repository;

import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.domain.model.enums.ScheduleStatus;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface ScheduleRepository extends MongoRepository<Schedule, String> {

    @Aggregation(pipeline = {
            "{ $match: { " +
                    "    'generation_time': { $gte: ?0, $lt: ?1 }, " +
                    "    'status': 'SCHEDULED' " +
                    "} }",
            "{ $group: { '_id': '$time_zone' } }",
            "{ $project: { '_id': 0, 'time_zone': '$_id' } }"
    })
    List<String> findDistinctTimeZonesForDateRange(Instant startTime, Instant endTime);

    List<Schedule> findByTimeZoneAndStatusAndGenerationTimeBetween(
            String timeZone,
            ScheduleStatus status,
            Instant startTime,
            Instant endTime
    );

    List<Schedule> findAllByStatus(ScheduleStatus status);

    Integer countByStatusInAndProjectDomain(List<ScheduleStatus> statuses, String projectDomain);

    Optional<Schedule> findByIdAndProjectDomain(String id, String projectDomain);
}
