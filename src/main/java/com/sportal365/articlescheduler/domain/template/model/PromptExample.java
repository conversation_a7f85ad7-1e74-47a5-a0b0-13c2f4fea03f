package com.sportal365.articlescheduler.domain.template.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;

@Data
@Document(collection = "prompt_examples")
public class PromptExample {

    @Id
    private String id;

    private String filledPromptTemplate;

    private Instant currentDate;

    @Field("match_date")
    private String date;

    @Field("match_time")
    private String time;

    private String matchName;
}
