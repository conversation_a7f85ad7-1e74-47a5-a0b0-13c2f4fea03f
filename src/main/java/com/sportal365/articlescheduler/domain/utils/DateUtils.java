package com.sportal365.articlescheduler.domain.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class DateUtils {

    public static Instant getDate(String fromDateStr, String timeZone) {
        if (fromDateStr != null && timeZone != null) {
            try {
                LocalDateTime localDateTime = LocalDateTime.parse(fromDateStr,
                        DateTimeFormatter.ISO_LOCAL_DATE_TIME);

                ZoneId zoneId = ZoneId.of(timeZone);

                ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);

                return zonedDateTime.toInstant();
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid from_date format or time zone: " + e.getMessage());
            }
        }
        return null;
    }
}
