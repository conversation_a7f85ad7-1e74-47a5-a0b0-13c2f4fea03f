package com.sportal365.articlescheduler.domain.widget.client;

import com.sportal365.articlescheduler.domain.widget.model.WidgetRequest;
import com.sportal365.articlescheduler.domain.widget.model.WidgetResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
@Slf4j
public class WidgetClient {

    private final WebClient widgetServiceWebClient;

    /**
     * Fetches a widget from the widget service
     *
     * @param widgetType The type of widget to fetch
     * @param widgetRequest The request containing widget parameters
     * @param projectHeader The project header for authentication
     * @param language The language for the widget content
     * @return A Mono with the widget response
     */
    public Mono<WidgetResponse> getWidget(String widgetType, WidgetRequest widgetRequest,
                                          String projectHeader, String language) {
        log.debug("Fetching widget of type: {} with ID: {}", widgetType, widgetRequest.getWidgetId());

        return widgetServiceWebClient.method(HttpMethod.POST)
                .uri("/api/widgets/{widgetType}", widgetType)
                .headers(headers -> {
                    headers.add("X-Project", projectHeader);
                    headers.add("Content-Type", "application/json");
                    if (widgetRequest.getLanguage() == null || widgetRequest.getLanguage().isEmpty()) {
                        headers.add("Accept-Language", language);
                    }
                })
                .bodyValue(widgetRequest)
                .retrieve()
                .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(), response ->
                        response.bodyToMono(String.class)
                                .map(error -> new RuntimeException("Widget API Error: " + error)))
                .bodyToMono(WidgetResponse.class)
                .doOnError(error -> log.error("Error: {} fetching widget of type: {} with ID: {}",
                        error.getMessage(), widgetType, widgetRequest.getWidgetId()))
                .doOnSuccess(response -> log.debug("Successfully fetched widget of type: {} with ID: {}",
                        widgetType, widgetRequest.getWidgetId()));
    }
}