package com.sportal365.articlescheduler.domain.widget.client.configuration;

import com.sportal365.articlescheduler.infrastructure.client.common.AbstractApiClientConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class WidgetClientConfiguration extends AbstractApiClientConfiguration {

    @Value("${widget.blocky.service.api.base.url}")
    private String baseUrl;

    @Bean
    public WebClient widgetServiceWebClient() {
        return createWebClient(baseUrl);
    }
}