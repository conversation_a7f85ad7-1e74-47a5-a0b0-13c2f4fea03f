package com.sportal365.articlescheduler.domain.widget.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)  // Only include non-null fields in JSON
public class WidgetRequest {
    private String widgetSport;
    private String widgetType;
    private String widgetId;
    private String competitionId;
    private String seasonId;
    private String stageId;
    private List<String> highlightTeams;
    private Boolean headerDisplay;
    private String offset;
    private String limit;
    private String refreshTime;
    private Boolean oddsDisplay;
    private Boolean fansUnitedExpanded;
    private Boolean fansUnitedEnabled;
    private Boolean singleEventMainEventsDisplay;
    private String oddsMarket;
    private String oddsBettingId;
    private String marketValueType;
    private String matchId;
    private String headerDefaultOption;
    private SportEntity sportEntityOne;
    private SportEntity sportEntityTwo;
    private List<String> elements;
    private String language;
    private Map<String, Object> models;
    private String project;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SportEntity {
        private String id;
        private String seasonId;
    }
}