package com.sportal365.articlescheduler.domain.widget.service;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.enums.WidgetTypeEnum;
import com.sportal365.articlescheduler.domain.widget.client.WidgetClient;
import com.sportal365.articlescheduler.domain.widget.model.WidgetRequest;
import com.sportal365.articlescheduler.domain.widget.model.WidgetResponse;
import com.sportal365.articlescheduler.infrastructure.client.contentapi.model.ArticleContent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class WidgetGenerationService {

    private static final String WIDGET_FORMAT = "<div>%s</div>";

    // Common widget types based on the curl examples

    private final WidgetClient widgetClient;

    /**
     * Creates an editor block for a widget based on the section and match details
     *
     * @param widgetType    The type of widget to create
     * @param matchDetails  The match details containing relevant data for the widget
     * @param projectHeader The project header for authentication
     * @param language      The language for the widget content
     * @return A Mono with the created ArticleBlock
     */
    public Mono<ArticleContent.ArticleBlock> createWidgetBlock(WidgetTypeEnum widgetType,
                                                               MatchDetails matchDetails,
                                                               String projectHeader, String language) {
        WidgetRequest widgetRequest = buildWidgetRequest(widgetType.getType(), matchDetails, language, projectHeader);

        return widgetClient.getWidget(widgetType.getType(), widgetRequest, projectHeader, language)
                .map(widgetResponse -> {
                    String blockId = UUID.randomUUID().toString();

                    // Extract content from the response structure
                    if (widgetResponse.getData() == null || widgetResponse.getData().getContent() == null) {
                        log.warn("Widget response contains no content for widget type: {}",
                                widgetType);
                    }
                    return createEditorBlock(widgetResponse);
                })
                .onErrorResume(error -> {
                    log.error("Failed to create widget block: {}", error.getMessage());
                    return Mono.empty(); // Skip this widget if there's an error
                });
    }

    private WidgetRequest buildWidgetRequest(String widgetType,
                                             MatchDetails matchDetails, String language, String project) {
        WidgetRequest.WidgetRequestBuilder requestBuilder = WidgetRequest.builder()
                .widgetSport("football")
                .widgetType(widgetType)
                .language(language)
                .project(project);

        switch (widgetType) {
            case "standings":
                requestBuilder
                        .competitionId(matchDetails.getTournamentId())
                        .highlightTeams(List.of(matchDetails.getHomeTeamId(), matchDetails.getAwayTeamId()))
                        .seasonId(matchDetails.getSeasonId());
                break;

            case "single-event":
                requestBuilder.matchId(matchDetails.getMatchId());
                break;

            case "team-h2h":
            case "matches-h2h":
                if (matchDetails.getHomeTeamId() != null && matchDetails.getAwayTeamId() != null) {
                    WidgetRequest.SportEntity homeTeam = new WidgetRequest.SportEntity(
                            matchDetails.getHomeTeamId(), matchDetails.getSeasonId());
                    WidgetRequest.SportEntity awayTeam = new WidgetRequest.SportEntity(
                            matchDetails.getAwayTeamId(), matchDetails.getSeasonId());

                    requestBuilder
                            .sportEntityOne(homeTeam)
                            .sportEntityTwo(awayTeam);
                }
                break;

            case "player-h2h":
                MatchDetails.ParticipantStatistics.Player homeTeamStar = matchDetails.getStatistics().getHomeTeam().getStarPlayer();
                MatchDetails.ParticipantStatistics.Player awayTeamStar = matchDetails.getStatistics().getAwayTeam().getStarPlayer();

                requestBuilder
                        .sportEntityOne(new WidgetRequest.SportEntity(homeTeamStar.getId(), matchDetails.getSeasonId()))
                        .sportEntityTwo(new WidgetRequest.SportEntity(awayTeamStar.getId(), matchDetails.getSeasonId()));
                break;

            default:
                // For any other widget types, add basic match details
                requestBuilder
                        .matchId(matchDetails.getMatchId())
                        .competitionId(matchDetails.getTournamentId())
                        .seasonId(matchDetails.getSeasonId())
                        .stageId(matchDetails.getStageId());
                break;
        }

        return requestBuilder.build();
    }

    private ArticleContent.ArticleBlock createEditorBlock(WidgetResponse widgetResponse) {
        return ArticleContent.ArticleBlock.builder()
                .id(widgetResponse.getId())
                .data(ArticleContent.BlockData.builder()
                        .widgetType(widgetResponse.getData().getWidgetType())
                        .sport(widgetResponse.getData().getSport())
                        .config(widgetResponse.getData().getConfig())
                        .changeId(widgetResponse.getData().getChangeId())
                        .preview(widgetResponse.getData().getPreview())
                        .content(widgetResponse.getData().getContent())
                        .build())
                .type(widgetResponse.getType())
                .build();
    }
}