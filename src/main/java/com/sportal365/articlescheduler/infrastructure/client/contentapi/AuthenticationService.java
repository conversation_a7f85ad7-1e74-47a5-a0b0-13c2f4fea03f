package com.sportal365.articlescheduler.infrastructure.client.contentapi;

import com.sportal365.articlescheduler.infrastructure.client.contentapi.model.TokenResponseDto;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class AuthenticationService {

    private final WebClient contentApiWebClient;
    @Value("${content.api.client-id}")
    private String clientId;
    @Value("${content.api.client-secret}")
    private String clientSecret;
    @Value("${content.api.username}")
    private String username;
    @Value("${content.api.password}")
    private String password;

    public Mono<String> getAccessToken() {
        return contentApiWebClient.post()
                .uri("/oauth/token")
                .bodyValue(Map.of(
                        "client_id", clientId,
                        "client_secret", clientSecret,
                        "grant_type", "password",
                        "username", username,
                        "password", password,
                        "scope", ""
                ))
                .retrieve()
                .bodyToMono(TokenResponseDto.class)
                .map(response -> response.getTokenType() + " " + response.getAccessToken());
    }

}

