package com.sportal365.articlescheduler.infrastructure.client.contentapi.configuration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class ContentApiConfiguration {

    @Value("${content.api.base.url}")
    private String contentApiBaseUrl;


    @Bean
    public WebClient contentApiWebClient(WebClient.Builder builder) {
        ExchangeStrategies strategies = ExchangeStrategies.builder()
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(100 * 1024 * 1024)) // 100MB
                .build();

        return builder
                .baseUrl(contentApiBaseUrl)
                .exchangeStrategies(strategies)
                .build();
    }
}
