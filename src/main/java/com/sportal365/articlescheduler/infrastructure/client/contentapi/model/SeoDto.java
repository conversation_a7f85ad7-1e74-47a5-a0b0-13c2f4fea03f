package com.sportal365.articlescheduler.infrastructure.client.contentapi.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class SeoDto {
    @JsonProperty("title")
    private String title;

    @JsonProperty("description")
    private String description;

    @JsonProperty("keywords")
    private List<String> keywords;

    private Boolean index;
    private Boolean follow;

    @JsonProperty("redirect_type")
    private String redirectType;
    private String jsonld;
    private String slug;

    @JsonProperty("automatic_seo_title")
    private Boolean automaticSeoTitle;

    @JsonProperty("automatic_slug")
    private Boolean automaticSlug;
}
