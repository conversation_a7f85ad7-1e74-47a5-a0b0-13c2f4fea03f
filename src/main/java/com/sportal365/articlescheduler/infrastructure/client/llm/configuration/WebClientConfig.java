package com.sportal365.articlescheduler.infrastructure.client.llm.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class WebClientConfig {

    @Value("${llm.service.api.base.url}")
    private String llmApiBaseUrl;

    @Bean
    public WebClient llmWebClient() {
        return WebClient.builder()
                .baseUrl(llmApiBaseUrl)
                .build();
    }
}
