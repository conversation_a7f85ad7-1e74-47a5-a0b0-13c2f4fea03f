package com.sportal365.articlescheduler.infrastructure.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;


@Configuration
@EnableMongoRepositories({"com.sportal365.articlescheduler.domain.repository",
        "com.sportal365.articlescheduler.infrastructure.persistence.repository"})
public class MongoConfiguration {}
