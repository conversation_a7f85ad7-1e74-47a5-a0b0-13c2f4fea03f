package com.sportal365.articlescheduler.infrastructure.persistence.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
@Document(collection = "templates")
@CompoundIndexes({
        @CompoundIndex(name = "template_unique_idx",
                def = "{'_id.projectDomain': 1, '_id.articleType': 1}",
                unique = true,
                sparse = true
        )
})
@Builder
public class TemplateDocument {
    @Id
    private TemplateId id;

    private String description;

    private String sport;

    @Field("prompt_template")
    private PromptTemplate promptTemplate;

    private String status;

    @Field("created_at")
    private Instant createdAt;

    @Field("updated_at")
    private Instant updatedAt;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PromptTemplate {
        @Field("prompt")
        private String prompt;

        @Field("parameters")
        private Parameters parameters;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Parameters {
        @Field("structure")
        private Structure structure;

        @Field("key_considerations")
        private KeyConsiderations keyConsiderations;

        @Field("exclusions")
        private List<String> exclusions;

        @Field("match_details")
        private Map<String, Object> matchDetails;

        @Field("output")
        private Output output;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Structure {
        @Field("title")
        private String title;

        @Field("summary")
        private String summary;

        @Field("sections")
        private List<Section> sections;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KeyConsiderations {
        @Field("direct_output")
        private String directOutput;

        @Field("journalistic_tone")
        private String journalisticTone;

        @Field("terminology")
        private String terminology;

        @Field("logical_flow")
        private String logicalFlow;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Output {
        @Field("format")
        private String format;
        @Field("language")
        private String language;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Section {
        @Field("section_id")
        private String sectionId;
        
        @Field("order_number")
        private String orderNumber;

        @Field("type")
        private String type;
    }
}
