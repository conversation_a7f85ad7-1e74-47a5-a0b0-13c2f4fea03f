package com.sportal365.articlescheduler.infrastructure.persistence.repository;

import com.sportal365.articlescheduler.domain.model.ParagraphTemplate;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ParagraphTemplateRepository extends MongoRepository<ParagraphTemplate, String> {

    Optional<ParagraphTemplate> findByName(String name);
}
