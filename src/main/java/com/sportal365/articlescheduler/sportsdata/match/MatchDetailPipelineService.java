package com.sportal365.articlescheduler.sportsdata.match;

import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.service.FootballService;
import com.sportal365.articlescheduler.sportsdata.sportal365.formguide.service.FormGuideService;
import com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.service.PlayoffsDataService;
import com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.service.SportSearchService;
import com.sportal365.articlescheduler.sportsdata.sportal365.standings.service.StandingsService;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.service.StatisticsService;
import com.sportal365.common.enums.SportEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class MatchDetailPipelineService {

    private final FormGuideService formGuideService;

    private final FootballService footballService;

    private final SportSearchService sportSearchService;

    private final StandingsService standingsService;

    private final PlayoffsDataService playoffsDataService;

    private final StatisticsService statisticsService;

    public List<MatchDetailEnrichService> getPipeline(Schedule schedule) {
        // TODO possibly add another check for pre_match type template;
        if (schedule.getSport() == SportEnum.FOOTBALL) {
            return List.of(sportSearchService, footballService,
                    standingsService, playoffsDataService,
                    formGuideService, statisticsService);
        }
        return Collections.emptyList();
    }
}
