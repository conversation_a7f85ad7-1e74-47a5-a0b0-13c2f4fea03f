package com.sportal365.articlescheduler.sportsdata.sportal365.football.client;

import com.sportal365.articlescheduler.domain.exception.ResourceNotFoundException;
import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.infrastructure.client.common.BaseApiClient;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.constants.FootballConstants;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model.LastMeetingsResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model.MatchDetailsResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model.MatchLineupResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model.TeamSquadResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class FootballApiClient extends BaseApiClient {

    public FootballApiClient(WebClient footballWebClient) {
        super(footballWebClient);
    }

    public Mono<MatchLineupResponse> getLineups(String matchId, String project) {
        
        validateRequiredParameter(FootballConstants.MATCH_ID, matchId);
        
        return handleApiCall(
                webClient.get()
                        .uri(uriBuilder -> uriBuilder
                                .path(FootballConstants.V2_MATCHES_LINEUPS)
                                .build(matchId))
                        .headers(headers -> configureHeaders(headers, project))
                        .retrieve()
                        .onStatus(HttpStatusCode::is4xxClientError, response -> handle4xxErrors(response, matchId))
                        .bodyToMono(MatchLineupResponse.class),
                "getLineups"
        );
    }

    public Mono<TeamSquadResponse> getTeamSquad(String teamId, String project) {
        validateRequiredParameter(FootballConstants.TEAM_ID, teamId);

        return handleApiCall(
                webClient.get()
                        .uri(uriBuilder -> uriBuilder
                                .path(FootballConstants.V2_TEAM_SQUAD)
                                .build(teamId))
                        .headers(headers -> configureHeaders(headers, project))
                        .retrieve()
                        .onStatus(HttpStatusCode::is4xxClientError, response -> handle4xxErrors(response, teamId))
                        .bodyToMono(TeamSquadResponse.class),
                "getTeamSquad"
        );
    }

    public Mono<MatchDetailsResponse> getMatchDetails(String matchId, String languageCode, String project) {
        validateRequiredParameter(FootballConstants.MATCH_ID, matchId);

        // Default to English if no language code is provided
        String language = (languageCode != null && !languageCode.isEmpty()) ? languageCode : "en";

        return handleApiCall(
                webClient.get()
                        .uri(uriBuilder -> uriBuilder
                                .path(FootballConstants.V2_MATCHES_DETAILS)
                                .queryParam("language_code", language)
                                .build(matchId))
                        .headers(headers -> configureHeaders(headers, project))
                        .retrieve()
                        .onStatus(HttpStatusCode::is4xxClientError, response -> handle4xxErrors(response, matchId))
                        .bodyToMono(MatchDetailsResponse.class),
                "getMatchDetails"
        );
    }


    private Mono<? extends Throwable> handle4xxErrors(ClientResponse response, String matchId) {
        if (response.statusCode() == HttpStatus.NOT_FOUND) {
            return Mono.error(new ResourceNotFoundException("Lineups not found for match: " + matchId));
        }
        return response.createException();
    }

    public Mono<LastMeetingsResponse> getMatchesBetweenTeams(
            List<String> teamIds, String sortDirection, String statusTypes, int limit, String languageCode,
            String project) {

        if (teamIds == null || teamIds.isEmpty()) {
            return Mono.error(new IllegalArgumentException("Team IDs must not be null or empty"));
        }

        String direction = (sortDirection != null && !sortDirection.isEmpty()) ? sortDirection : "desc";
        String status = (statusTypes != null && !statusTypes.isEmpty()) ? statusTypes : "FINISHED";
        int matchLimit = (limit > 0) ? limit : 1;

        return handleApiCall(
                webClient.get()
                        .uri(uriBuilder -> uriBuilder
                                .path(FootballConstants.V2_MATCHES_LIST)
                                .queryParam("offset", 0)
                                .queryParam("limit", matchLimit)
                                .queryParam("team_ids", String.join(",", teamIds))
                                .queryParam("sort_direction", direction)
                                .queryParam("status_types", status)
                                .queryParam("language_code", languageCode)
                                .queryParam("optional_data", "MAIN_EVENTS")
                                .build())
                        .headers(headers -> configureHeaders(headers, project))
                        .retrieve()
                        .onStatus(HttpStatusCode::is4xxClientError, response -> {
                            log.error("Client error occurred. Status: {}", response.statusCode());
                            return response.bodyToMono(String.class)
                                    .flatMap(errorBody -> Mono.error(new ResourceNotFoundException(
                                            "Error fetching matches: " + errorBody)));
                        })
                        .bodyToMono(LastMeetingsResponse.class),
                "getMatchesBetweenTeams"
        );
    }

    public Mono<Map<String, Object>> getMatchTagRelated(MatchDetails matchDetails, String translationLanguage, String project) {
        validateRequiredParameter("Legacy ID", matchDetails.getLegacyId());

        return handleApiCall(
                webClient.get()
                        .uri(uriBuilder -> uriBuilder
                                .path(FootballConstants.V2_MATCHES_DETAILS)
                                .queryParam("language_code", translationLanguage)
                                .build(matchDetails.getLegacyId()))
                        .headers(headers -> configureHeaders(headers, project))
                        .retrieve()
                        .onStatus(HttpStatusCode::is4xxClientError, response -> {
                            log.error("Client error occurred. Status: {}", response.statusCode());
                            return response.bodyToMono(String.class)
                                    .flatMap(errorBody -> Mono.error(new ResourceNotFoundException(
                                            String.format("Error fetching match details for ID %s: %s",
                                                    matchDetails.getLegacyId(), errorBody))));
                        })
                        .bodyToMono(new ParameterizedTypeReference<Map<String, Object>>() {}),
                "getMatchTagRelated"
        );
    }
}
