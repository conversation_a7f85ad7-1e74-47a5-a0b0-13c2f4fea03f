package com.sportal365.articlescheduler.sportsdata.sportal365.football.client.configuration;

import com.sportal365.articlescheduler.infrastructure.client.common.AbstractApiClientConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class FootballApiClientConfiguration extends AbstractApiClientConfiguration {

    @Value("${football.api.base.url}")
    private String footballApiBaseUrl;

    @Value("${football.api.username}")
    private String username;

    @Value("${football.api.password}")
    private String password;

    @Bean
    public WebClient footballWebClient() {
        return createWebClient(footballApiBaseUrl, username, password);
    }
}
