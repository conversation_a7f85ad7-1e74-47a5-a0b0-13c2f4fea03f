package com.sportal365.articlescheduler.sportsdata.sportal365.football.client.enums;

public enum LineupStatus {
	
	CONFIRMED, NOT_AVAILABLE, UNCONFIRMED;

	public static LineupStatus fromString(String status) {
		if (status == null) {
			return NOT_AVAILABLE;
		}

		String normalized = status.toUpperCase();
		try {
			return LineupStatus.valueOf(normalized);
		} catch (IllegalArgumentException ex) {
			// Unknown value → fallback
			return NOT_AVAILABLE;
		}
	}
}
