package com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class LastMeetingsResponse {

    private List<Match> matches;

    @JsonProperty("page_meta")
    private PageMeta pageMeta;

    @Data
    public static class PageMeta {
        private int total;
        private int offset;
        private int limit;
    }

    @Data
    public static class Match {
        private String id;
        private String slug;
        private Status status;

        @JsonProperty("kickoff_time")
        private String kickoffTime;

        private Stage stage;
        private Season season;
        private Round round;

        @JsonProperty("home_team")
        private Team homeTeam;

        @JsonProperty("away_team")
        private Team awayTeam;

        private List<Referee> referees;
        private Venue venue;
        private Integer spectators;
        private String coverage;
        private String minute;

        @JsonProperty("phase_started_at")
        private String phaseStartedAt;

        @JsonProperty("finished_at")
        private String finishedAt;

        private Score score;

        @JsonProperty("main_events")
        private List<MainEvent> mainEvents;

        private Winner winner;
        private String uuid;
        private List<Object> odds;

        @JsonProperty("first_half_started_at")
        private String firstHalfStartedAt;

        @JsonProperty("second_half_started_at")
        private String secondHalfStartedAt;

        @JsonProperty("extra_time_first_half_started_at")
        private String extraTimeFirstHalfStartedAt;

        @JsonProperty("extra_time_second_half_started_at")
        private String extraTimeSecondHalfStartedAt;
    }

    @Data
    public static class MainEvent {
        private String id;

        @JsonProperty("match_id")
        private String matchId;

        @JsonProperty("type_code")
        private String typeCode;

        @JsonProperty("team_position")
        private String teamPosition;

        private Integer minute;

        @JsonProperty("injury_minute")
        private Integer injuryMinute;

        @JsonProperty("injury_time_minutes")
        private Integer injuryTimeMinutes;

        @JsonProperty("team_id")
        private String teamId;

        @JsonProperty("primary_player")
        private Player primaryPlayer;

        @JsonProperty("secondary_player")
        private Player secondaryPlayer;

        private ScoreValue score;
    }

    @Data
    public static class Player {
        private String id;
        private String name;
        private String slug;
        private String position;
        private String gender;
        private Assets assets;
    }

    @Data
    public static class Status {
        private String id;
        private String name;

        @JsonProperty("short_name")
        private String shortName;

        private String type;
        private String code;
        private String uuid;
    }

    @Data
    public static class Stage {
        private String id;
        private String name;
        private String slug;
        private String type;

        @JsonProperty("start_date")
        private String startDate;

        @JsonProperty("end_date")
        private String endDate;

        @JsonProperty("order_in_season")
        private Integer orderInSeason;

        private String coverage;

        @JsonProperty("short_name")
        private String shortName;

        private String uuid;
    }

    @Data
    public static class Season {
        private String id;
        private String name;
        private String slug;
        private Tournament tournament;
        private String status;
        private String uuid;
    }

    @Data
    public static class Tournament {
        private String id;
        private String name;
        private String slug;
        private Country country;
        private String gender;
        private String type;
        private String region;
        private Assets assets;
        private String uuid;
    }

    @Data
    public static class Country {
        private String id;
        private String name;
        private String slug;
        private String code;
        private Assets assets;
        private String uuid;
    }

    @Data
    public static class Assets {
        private Image logo;
        private Image flag;
        private Image image;

        @JsonProperty("home_kit")
        private Image homeKit;

        @JsonProperty("away_kit")
        private Image awayKit;

        @JsonProperty("squad_image")
        private Image squadImage;
    }

    @Data
    public static class Image {
        private String url;
    }

    @Data
    public static class Round {
        private String key;
        private String name;
        private String type;
        private String uuid;
    }

    @Data
    public static class Team {
        private String id;
        private String name;
        private String slug;

        @JsonProperty("three_letter_code")
        private String threeLetterCode;

        private String gender;

        @JsonProperty("short_name")
        private String shortName;

        private String type;

        @JsonProperty("shirt_color")
        private String shirtColor;

        private Assets assets;
        private String uuid;
    }

    @Data
    public static class Referee {
        private String id;
        private String name;
        private String slug;
        private String role;
        private String gender;
        private Assets assets;
    }

    @Data
    public static class Venue {
        private String id;
        private String name;
        private String slug;
        private Assets assets;
        private String uuid;
    }

    @Data
    public static class Score {
        private ScoreValue total;

        @JsonProperty("half_time")
        private ScoreValue halfTime;

        @JsonProperty("regular_time")
        private ScoreValue regularTime;

        @JsonProperty("extra_time")
        private ScoreValue extraTime;

        @JsonProperty("penalty_shootout")
        private ScoreValue penaltyShootout;

        private ScoreValue aggregate;

        @JsonProperty("after_extra_time")
        private ScoreValue afterExtraTime;

        private ScoreValue display;
    }

    @Data
    public static class ScoreValue {
        private Integer home;
        private Integer away;
    }

    @Data
    public static class Winner {
        private MatchWinner match;
        private Object aggregate;

        @Data
        public static class MatchWinner {
            private String id;
            private String reason;
        }
    }
}
