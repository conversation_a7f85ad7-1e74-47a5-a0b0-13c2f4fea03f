package com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model;

import lombok.Data;

import java.util.List;

@Data
public class LineupResponse {
    
    private String matchId;
    private String status;
    private TeamDetails homeTeam;
    private TeamDetails awayTeam;
    
    @Data
    public static class TeamDetails {
        private String formation;
        private Coach coach;
        private String teamId;
        private List<Player> players;
        
        @Data
        public static class Coach {
            private String id;
            private String name;
            private String slug;
            private Country country;
            private String birthdate;
            private String gender;
            private Assets assets;
            
            @Data
            public static class Assets {
                private Image thumb;
                
                @Data
                public static class Image {
                    private String url;
                }
            }
            
            @Data
            public static class Country {
                private String id;
                private String name;
                private String slug;
                private String uuid;
                private Assets assets;
                
                @Data
                public static class Assets {
                    private Image flag;
                    
                    @Data
                    public static class Image {
                        private String url;
                    }
                }
            }
        }
        
        @Data
        public static class Player {
            private Type type;
            private PlayerDetails player;
            private Integer positionX;
            private Integer positionY;
            private Integer shirtNumber;
            private String uuid;
            
            @Data
            public static class Type {
                private String id;
                private String name;
                private String category;
                private String code;
                private String uuid;
            }
            
            @Data
            public static class PlayerDetails {
                private Country country;
                private boolean active;
                private String birthdate;
                private String position;
                private Profile profile;
                private Assets assets;
                private String id;
                private String name;
                private String slug;
                private String uuid;
                
                @Data
                public static class Country {
                    private String id;
                    private String name;
                    private String slug;
                    private String uuid;
                    private Assets assets;
                    
                    @Data
                    public static class Assets {
                        private Image flag;
                        
                        @Data
                        public static class Image {
                            private String url;
                        }
                    }
                }
                
                @Data
                public static class Profile {
                    private String height;
                    private String weight;
                }
                
                @Data
                public static class Assets {
                    private Image thumb;
                    private Image image;
                    
                    @Data
                    public static class Image {
                        private String url;
                    }
                }
            }
        }
    }
}