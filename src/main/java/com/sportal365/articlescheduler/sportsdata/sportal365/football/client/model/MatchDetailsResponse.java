package com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class MatchDetailsResponse {

    private String id;
    private String slug;
    private Status status;

    @JsonProperty("kickoff_time")
    private String kickoffTime;

    private Stage stage;
    private Season season;
    private Round round;

    @JsonProperty("home_team")
    private Team homeTeam;

    @JsonProperty("away_team")
    private Team awayTeam;

    private List<Referee> referees;
    private Venue venue;
    private String spectators;
    private String coverage;
    private String minute;
    private String uuid;

    @Data
    public static class Status {
        private String id;
        private String name;

        @JsonProperty("short_name")
        private String shortName;

        private String type;
        private String code;
        private String uuid;
    }

    @Data
    public static class Stage {
        private String id;
        private String name;
        private String slug;
        private String type;

        @JsonProperty("start_date")
        private String startDate;

        @JsonProperty("end_date")
        private String endDate;

        @JsonProperty("order_in_season")
        private Integer orderInSeason;

        private String coverage;

        @JsonProperty("short_name")
        private String shortName;

        private String uuid;
    }

    @Data
    public static class Season {
        private String id;
        private String name;
        private String slug;
        private Tournament tournament;
        private String status;
        private String uuid;
    }

    @Data
    public static class Tournament {
        private String id;
        private String name;
        private String slug;
        private Country country;
        private String gender;
        private String type;
        private String region;
        private Assets assets;
        private String uuid;
    }

    @Data
    public static class Country {
        private String id;
        private String name;
        private String slug;
        private String code;
        private Assets assets;
        private String uuid;
    }

    @Data
    public static class Assets {
        private Image logo;
        private Image flag;
        private Image image;

        @JsonProperty("home_kit")
        private Image homeKit;

        @JsonProperty("away_kit")
        private Image awayKit;
    }

    @Data
    public static class Image {
        private String url;
    }

    @Data
    public static class Round {
        private String key;
        private String name;
        private String type;
        private String uuid;
    }

    @Data
    public static class Team {
        private String id;
        private String name;
        private String slug;

        @JsonProperty("three_letter_code")
        private String threeLetterCode;

        private String gender;

        @JsonProperty("short_name")
        private String shortName;

        private String type;

        @JsonProperty("shirt_color")
        private String shirtColor;

        private Assets assets;
        private String uuid;
    }

    @Data
    public static class Referee {
        private String id;
        private String name;
        private String slug;
        private String type;
        private Country country;
        private String uuid;
    }

    @Data
    public static class Venue {
        private String id;
        private String name;
        private String slug;
        private Assets assets;
        private String uuid;
    }
}
