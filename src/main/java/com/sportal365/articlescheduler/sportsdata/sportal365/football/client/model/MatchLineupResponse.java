package com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class MatchLineupResponse {
	
	@JsonProperty("match_id")
	private String matchId;
	
	@JsonProperty("status")
	private String status;
	
	@JsonProperty("home_team")
	private Team homeTeam;
	
	@JsonProperty("away_team")
	private Team awayTeam;
	
	@Data
	public static class Team {
		private String formation;
		private Coach coach;
		
		@JsonProperty("team_id")
		private String teamId;
		private List<Player> players;
		
		@Data
		public static class Coach {
			private String id;
			private String name;
			private String slug;
			private Country country;
			private String birthdate;
			private String gender;
			private Assets assets;
			
			@Data
			public static class Country {
				private String id;
				private String name;
				private String slug;
				private String uuid;
				private Assets assets;
				
				@Data
				public static class Assets {
					private Flag flag;
					
					@Data
					public static class Flag {
						private String url;
					}
				}
			}
			
			@Data
			public static class Assets {
				private Image thumb;
				
				@Data
				public static class Image {
					private String url;
				}
			}
		}
		
		@Data
		public static class Player {
			private Type type;
			private PlayerDetails player;
			
			@JsonProperty("position_x")
			private Integer positionX;
			
			@JsonProperty("position_y")
			private Integer positionY;
			
			@JsonProperty("shirt_number")
			private Integer shirtNumber;
			private String uuid;
			
			@Data
			public static class Type {
				private String id;
				private String name;
				private String category;
				private String code;
				private String uuid;
			}
			
			@Data
			public static class PlayerDetails {
				private Country country;
				private boolean active;
				private String birthdate;
				
				@JsonProperty("birth_city")
				private String birthCity;
				private String position;
				private Profile profile;
				private Assets assets;
				private String id;
				private String name;
				private String slug;
				private String uuid;
				
				@Data
				public static class Country {
					private String id;
					private String name;
					private String slug;
					private String uuid;
					private Assets assets;
					
					@Data
					public static class Assets {
						private Flag flag;
						
						@Data
						public static class Flag {
							private String url;
						}
					}
				}
				
				@Data
				public static class Profile {
					private String height;
					private String weight;
				}
				
				@Data
				public static class Assets {
					private Image thumb;
					private Image image;
					
					@Data
					public static class Image {
						private String url;
					}
				}
			}
		}
	}

}
