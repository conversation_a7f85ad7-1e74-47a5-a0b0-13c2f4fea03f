package com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model;

import lombok.Getter;

@Getter
public enum SportConnectionType {

    MATCH("v2/matches"),
    TOURNAMENT("tournaments"),
    PLAYER("v2/players"),
    COACH("v2/coaches"),
    TEAM("v2/teams"),
    VENUE("v2/venues");

    SportConnectionType(String value) {
        this.value = value;
    }

    private final String value;

}
