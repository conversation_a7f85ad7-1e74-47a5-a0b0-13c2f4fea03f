package com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class TeamSquadResponse {
    private Team team;
    private List<PlayerWrapper> players;

    @Data
    public static class Team {
        @JsonProperty("uuid")
        private String internalId;
        private String name;
    }

    @Data
    public static class PlayerWrapper {
        private Player player;
        private String status;
    }

    @Data
    public static class Player {
        @JsonProperty("uuid")
        private String internalId;
        private String name;
        private String position;
    }
}
