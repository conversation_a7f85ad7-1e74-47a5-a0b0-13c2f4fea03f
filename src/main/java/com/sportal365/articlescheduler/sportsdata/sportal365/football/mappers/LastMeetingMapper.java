package com.sportal365.articlescheduler.sportsdata.sportal365.football.mappers;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model.LastMeetingsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class LastMeetingMapper {

    public MatchDetails.LastMeeting map(LastMeetingsResponse lastMeetingsResponse) {

        if (lastMeetingsResponse == null || lastMeetingsResponse.getMatches() == null ||
                lastMeetingsResponse.getMatches().isEmpty()) {

            return null;
        }
        LastMeetingsResponse.Match match = lastMeetingsResponse.getMatches().get(0);

        String homeTeamId = lastMeetingsResponse.getMatches().get(0).getHomeTeam().getId();
        MatchDetails.LastMeeting.Scorers goalScorers = extractScorers(
                lastMeetingsResponse.getMatches().get(0).getMainEvents(), homeTeamId);

        return MatchDetails.LastMeeting.builder()
                .id(match.getUuid())
                .finalResult(getFinalResult(match))
                .regularTimeResult(getRegularTimeResult(match))
                .afterExtraTimeResult(getAfterExtraTimeResult(match))
                .penaltyResult(getPenaltyResult(match))
                .aggregateResult(getAggregateResult(match))
                .competitionName(extractCompetitionName(match))
                .competitionType(extractCompetitionType(match))
                .date(match.getKickoffTime())
                .roundName(extractRoundName(match))
                .roundType(extractRoundType(match))
                .venue(extractVenueName(match))
                .scorers(goalScorers)
                .build();
    }

    public List<MatchDetails.LastMeeting> mapToList(LastMeetingsResponse lastMeetingsResponse) {
        if (lastMeetingsResponse == null || lastMeetingsResponse.getMatches() == null ||
                lastMeetingsResponse.getMatches().isEmpty()) {
            return Collections.emptyList();
        }

        return lastMeetingsResponse.getMatches().stream()
                .map(match -> MatchDetails.LastMeeting.builder()
                        .id(match.getUuid())
                        .finalResult(getFinalResult(match))
                        .regularTimeResult(getRegularTimeResult(match))
                        .afterExtraTimeResult(getAfterExtraTimeResult(match))
                        .penaltyResult(getPenaltyResult(match))
                        .aggregateResult(getAggregateResult(match))
                        .competitionName(extractCompetitionName(match))
                        .competitionType(extractCompetitionType(match))
                        .date(match.getKickoffTime())
                        .roundName(extractRoundName(match))
                        .roundType(extractRoundType(match))
                        .venue(extractVenueName(match))
                        .scorers(extractScorers(
                                match.getMainEvents(),
                                match.getHomeTeam().getId()
                        ))
                        .build()
                )
                .collect(Collectors.toList());
    }

    private String getFinalResult(LastMeetingsResponse.Match match) {
        if (match.getScore().getTotal().getHome() == null || match.getScore().getTotal().getAway() == null) {

            return "Not Available";
        }
        return getResult(match, match.getScore().getTotal().getHome(), match.getScore().getTotal().getAway());
    }

    private String getRegularTimeResult(LastMeetingsResponse.Match match) {

        if (match.getScore().getRegularTime() == null) {
            return "Not Available";
        }

        return getResult(match, match.getScore().getRegularTime().getHome(), match.getScore().getRegularTime().getAway());
    }

    private String getAfterExtraTimeResult(LastMeetingsResponse.Match match) {

        if (match.getScore().getAfterExtraTime() == null) {

            return "Not Available";
        }

        return getResult(match, match.getScore().getAfterExtraTime().getHome(), match.getScore().getAfterExtraTime().getAway());
    }

    private String getPenaltyResult(LastMeetingsResponse.Match match) {

        if (match.getScore().getPenaltyShootout() == null) {

            return "Not Available";
        }
        return getResult(match, match.getScore().getPenaltyShootout().getHome(), match.getScore().getPenaltyShootout().getAway());
    }

    private String getAggregateResult(LastMeetingsResponse.Match match) {

        if (match.getScore().getAggregate() == null) {

            return "Not Available";
        }

        return getResult(match, match.getScore().getAggregate().getHome(), match.getScore().getAggregate().getAway());
    }



    /**
     * Extract goalscorers and penalty takers from main events
     *
     * @param mainEvents List of main events from the match
     * @param homeTeamId ID of the home team to differentiate home/away players
     * @return Scorers object with lists of home and away team scorers
     */
    private MatchDetails.LastMeeting.Scorers extractScorers(List<LastMeetingsResponse.MainEvent> mainEvents, String homeTeamId) {

        List<String> homeTeamScorers = new ArrayList<>();
        List<String> awayTeamScorers = new ArrayList<>();

        if (mainEvents == null || mainEvents.isEmpty()) {
            return MatchDetails.LastMeeting.Scorers.builder()
                    .homeTeam(Collections.emptyList())
                    .awayTeam(Collections.emptyList())
                    .build();
        }

        for (LastMeetingsResponse.MainEvent event : mainEvents) {
            if (event.getPrimaryPlayer() == null) {
                continue;
            }

            String typeCode = event.getTypeCode();

            // Only process goals and penalties
            if (!("GOAL".equals(typeCode) || "PENALTY_GOAL".equals(typeCode)) ) {
                continue;
            }

            String playerName = event.getPrimaryPlayer().getName();
            Integer minute = event.getMinute();
            String scoreInfo;

            // Format differently based on an event type
            if ("GOAL".equals(typeCode)) {
                scoreInfo = String.format("%s (%d')", playerName, minute);
            } else  {
                scoreInfo = String.format("%s (pen. scored)", playerName);
            }

            // Add to the appropriate team list
            if (homeTeamId.equals(event.getTeamId())) {
                homeTeamScorers.add(scoreInfo);
            } else {
                awayTeamScorers.add(scoreInfo);
            }
        }

        return MatchDetails.LastMeeting.Scorers.builder()
                .homeTeam(homeTeamScorers)
                .awayTeam(awayTeamScorers)
                .build();

    }

    /**
     * Format match result including team names and scores
     */
    private String getResult(LastMeetingsResponse.Match match, Integer homeScore, Integer awayScore) {
        if (match.getScore() == null || match.getScore().getDisplay() == null) {
            return null;
        }

        String homeTeam = Optional.ofNullable(match.getHomeTeam())
                .map(LastMeetingsResponse.Team::getName)
                .orElse("Home");

        String awayTeam = Optional.ofNullable(match.getAwayTeam())
                .map(LastMeetingsResponse.Team::getName)
                .orElse("Away");

        if (homeScore == null || awayScore == null) {
            return null;
        }

        return String.format("%s %d-%d %s", homeTeam, homeScore, awayScore, awayTeam);
    }

    /**
     * Extract competition type from a stage or tournament
     */
    private String extractCompetitionType(LastMeetingsResponse.Match match) {
        return Optional.ofNullable(match.getSeason())
                .map(LastMeetingsResponse.Season::getTournament)
                .map(LastMeetingsResponse.Tournament::getType)
                .orElse(null);
    }

    /**
     * Extract round name
     */
    private String extractCompetitionName(LastMeetingsResponse.Match match) {
        return Optional.ofNullable(match.getSeason())
                .map(LastMeetingsResponse.Season::getTournament)
                .map(LastMeetingsResponse.Tournament::getName)
                .orElse("");

    }

    /**
     * Extract round type
     */
    private String extractRoundName(LastMeetingsResponse.Match match) {
        return Optional.ofNullable(match.getRound())
                .map(LastMeetingsResponse.Round::getName)
                .orElse("");
    }

    private String extractRoundType(LastMeetingsResponse.Match match) {
        return Optional.ofNullable(match.getRound())
                .map(LastMeetingsResponse.Round::getType)
                .orElse("");
    }

    private String extractVenueName(LastMeetingsResponse.Match match) {
        return Optional.ofNullable(match.getVenue())
                .map(LastMeetingsResponse.Venue::getName)
                .orElse("");
    }
}
