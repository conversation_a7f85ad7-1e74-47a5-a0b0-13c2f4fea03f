package com.sportal365.articlescheduler.sportsdata.sportal365.football.mappers;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model.MatchDetailsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
public class MatchDetailsMapper {

    public MatchDetails enhanceWithRefereeAndVenue(MatchDetails matchDetails, MatchDetailsResponse matchDetailsResponse) {
        if (matchDetailsResponse == null) {
            log.warn("EventDetailsResponse is null, returning null");
            return null;
        }

        matchDetails.setReferee(getReferee(matchDetailsResponse));
        matchDetails.setVenue(getVenue(matchDetailsResponse));

        return matchDetails;
    }

    public String getReferee(MatchDetailsResponse matchDetailsResponse) {
        return Optional.ofNullable(matchDetailsResponse)
                .map(MatchDetailsResponse::getReferees)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(MatchDetailsResponse.Referee::getName)
                .orElse(null);
    }

    public String getVenue(MatchDetailsResponse matchDetailsResponse) {
        return Optional.ofNullable(matchDetailsResponse)
                .map(MatchDetailsResponse::getVenue)
                .map(MatchDetailsResponse.Venue::getName)
                .orElse(null);
    }
}


