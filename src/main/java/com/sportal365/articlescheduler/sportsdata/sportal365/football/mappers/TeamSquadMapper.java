package com.sportal365.articlescheduler.sportsdata.sportal365.football.mappers;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model.TeamSquadResponse;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TeamSquadMapper {

    private static final String PLAYER_STATUS_ACTIVE = "ACTIVE";

    public MatchDetails.Squad map(List<TeamSquadResponse> teamSquadResponses) {
        if (teamSquadResponses == null || teamSquadResponses.size() != 2) {
            return null;
        }

        return MatchDetails.Squad.builder()
                .homeTeam(mapTeam(teamSquadResponses.get(0)))
                .awayTeam(mapTeam(teamSquadResponses.get(1)))
                .build();
    }

    private MatchDetails.Squad.Team mapTeam(TeamSquadResponse teamSquadResponse) {
        List<MatchDetails.Squad.Player> squadPlayers = teamSquadResponse.getPlayers().stream()
                .map(playerWrapper -> MatchDetails.Squad.Player.builder()
                        .id(playerWrapper.getPlayer().getInternalId())
                        .name(playerWrapper.getPlayer().getName())
                        .position(playerWrapper.getPlayer().getPosition())
                        .active(playerWrapper.getStatus().equalsIgnoreCase(PLAYER_STATUS_ACTIVE))
                        .build())
                .toList();

        return MatchDetails.Squad.Team.builder()
                .id(teamSquadResponse.getTeam().getInternalId())
                .name(teamSquadResponse.getTeam().getName())
                .players(squadPlayers)
                .build();
    }

}
