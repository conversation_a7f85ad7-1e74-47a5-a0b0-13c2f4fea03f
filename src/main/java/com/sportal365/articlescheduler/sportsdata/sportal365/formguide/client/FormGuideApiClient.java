package com.sportal365.articlescheduler.sportsdata.sportal365.formguide.client;

import com.sportal365.articlescheduler.infrastructure.client.common.BaseApiClient;
import com.sportal365.articlescheduler.sportsdata.sportal365.formguide.constants.FormGuideConstants;
import com.sportal365.articlescheduler.sportsdata.sportal365.formguide.model.FormGuideResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriBuilder;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.function.Function;

import static com.sportal365.articlescheduler.sportsdata.sportal365.formguide.constants.FormGuideConstants.SPORT;

@Slf4j
@Component
public class FormGuideApiClient extends BaseApiClient {

    public FormGuideApiClient(WebClient formGuideApiWebClient) {
        super(formGuideApiWebClient);
    }

    public Mono<FormGuideResponse> getTeamForm(String sport, String participantId, String projectHeader, String language) {
        validateRequiredParameter(SPORT, sport);
        return handleApiCall(
                webClient.get()
                        .uri(buildFormGuideUri(sport, participantId, language))
                        .headers(headers -> configureHeaders(headers, projectHeader))
                        .accept(MediaType.APPLICATION_JSON)
                        .retrieve()
                        .bodyToMono(FormGuideResponse.class),
                "getTeamForm"
        );
    }

    private Function<UriBuilder, URI> buildFormGuideUri(String sport, String participantId, String language) {
        return uriBuilder -> {
            UriBuilder builder = uriBuilder
                    .path(FormGuideConstants.EVENT_ENDPOINT)
                    .queryParam(SPORT, sport);

            addOptionalQueryParam(builder, FormGuideConstants.PARTICIPANT_ID, participantId);
            addOptionalQueryParam(builder, FormGuideConstants.TRANSLATION_LANGUAGE, language);

            return builder.build();
        };
    }


    private void addOptionalQueryParam(UriBuilder builder, String name, String value) {
        if (value != null && !value.isEmpty()) {
            builder.queryParam(name, value);
        }
    }


}
