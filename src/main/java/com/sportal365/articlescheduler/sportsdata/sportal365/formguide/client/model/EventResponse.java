package com.sportal365.articlescheduler.sportsdata.sportal365.formguide.client.model;

import lombok.Data;

import java.util.List;

@Data
public class EventResponse {
    private String id;
    private StatusResponse status;
    private List<ResultResponse> result;
    private String outcome;
    private List<ParticipantWithPositionResponse> participants;
    private String slug;
    private String startTime;
    private String legacyId;
}
