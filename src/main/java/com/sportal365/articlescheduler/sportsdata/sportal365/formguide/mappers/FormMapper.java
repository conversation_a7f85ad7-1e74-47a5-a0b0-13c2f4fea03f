package com.sportal365.articlescheduler.sportsdata.sportal365.formguide.mappers;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.sportal365.formguide.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class FormMapper {

    public MatchDetails.RecentMatches map(List<FormGuideResponse> response) {

        // Defensive check: you expect exactly 2 items: home (index 0) and away (index 1)
        if (response == null || response.size() != 2) {
            // Return empty if data is insufficient
            return MatchDetails.RecentMatches.builder()
                    .homeTeam(List.of())
                    .awayTeam(List.of())
                    .build();
        }

        // 1) The home participant’s events
        FormGuideResponse home = response.get(0);
        // 2) The away participant’s events
        FormGuideResponse away = response.get(1);

        // Build the "homeTeam" list of recent matches
        List<MatchDetails.RecentMatches.MatchSummary> homeSummaries = toMatchSummaries(home);

        // Build the "awayTeam" list of recent matches
        List<MatchDetails.RecentMatches.MatchSummary> awaySummaries = toMatchSummaries(away);

        // Build and return the final RecentMatches object
        return MatchDetails.RecentMatches.builder()
                .homeTeam(homeSummaries)
                .awayTeam(awaySummaries)
                .build();
    }

    private List<MatchDetails.RecentMatches.MatchSummary> toMatchSummaries(FormGuideResponse formGuide) {
        try {
            if (formGuide == null || formGuide.getData() == null || formGuide.getData().isEmpty()) {
                log.warn("FormGuide is null or empty");
                return List.of();
            }

            ParticipantEventDto participantEvent = formGuide.getData().get(0);
            if (participantEvent == null || participantEvent.getEvents() == null) {
                log.warn("Participant event is null or has no events");
                return List.of();
            }

            String participantId = Optional.ofNullable(participantEvent.getParticipant())
                    .map(ParticipantDto::getId)
                    .orElse(null);

            if (participantId == null) {
                log.warn("Participant ID is null");
                return List.of();
            }

            // 3. Now map each EventDto to a MatchSummary
            return participantEvent.getEvents().stream()
                    .limit(5)  // for example, last 5 matches
                    .map(event -> {
                        // a) Map outcome to W/D/L
                        String mappedOutcome = mapOutcome(event.getOutcome());

                        // If mapped outcome is null, return null MatchSummary
                        if (mappedOutcome == null) {
                            return null;
                        }

                        // b) Extract or build the score from event.getResult()
                        String score = extractScore(event);

                        // c) Figure out the opponent name
                        String opponent = extractOpponent(event, participantId);

                        // d) Date from event.getStartTime() or another field
                        String date = extractDate(event);

                        // Competition name for the event
                        String competitionName = event.getCompetition().getName();

                        // e) Build the MatchSummary
                        return MatchDetails.RecentMatches.MatchSummary.builder()
                                .opponent(opponent)
                                .result(mappedOutcome)
                                .score(score)
                                .date(date)
                                .competitionName(competitionName)
                                .build();
                    })
                    .filter(Objects::nonNull)  // Remove null entries
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error processing form guide: ", e);
            return List.of();
        }
    }


    private static final Map<String, String> OUTCOME_MAP = Map.of(
        "win", "W",
        "loss", "L", 
        "draw", "D"
    );

    private String mapOutcome(String outcome) {
        return outcome == null 
            ? null 
            : OUTCOME_MAP.get(outcome.toLowerCase());
    }

    private String extractScore(EventDto event) {
        if (event.getResult() == null || event.getResult().isEmpty()) {
            return "N/A";
        }
        // Take the first ResultDto, stream its ResultPositionDto values, and join them with ":"
        return event.getResult().stream()
                .findFirst()
                .map(resultDto -> resultDto.getResults().stream()
                        .map(ResultPositionDto::getValue)
                        .collect(Collectors.joining(":")))
                .orElse("N/A");
    }

    private String extractOpponent(EventDto event, String participantId) {
        if (event.getParticipants() == null || participantId == null) {
            return null;
        }

        return event.getParticipants().stream()
            .filter(Objects::nonNull)
            .map(p -> (ParticipantDto) p)
            .filter(p -> !participantId.equals(p.getId()))
            .map(ParticipantDto::getName)
            .findFirst()
            .orElse(null);
    }

    private String extractDate(EventDto event) {
        return Optional.ofNullable(event.getStartTime())
            .orElse("Unknown Date");
    }
}
