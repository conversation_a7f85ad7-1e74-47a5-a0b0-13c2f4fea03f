package com.sportal365.articlescheduler.sportsdata.sportal365.formguide.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class EventDto {
    private String id;
    private StatusDto status;
    private List<ResultDto> result;
    private String outcome;
    private List<ParticipantWithPositionDto> participants;
    private String slug;
    @JsonProperty("start_time")
    private String startTime;
    @JsonProperty("legacy_id")
    private String legacyId;
    private CompetitionDto competition;
}
