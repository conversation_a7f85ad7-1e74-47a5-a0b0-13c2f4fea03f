package com.sportal365.articlescheduler.sportsdata.sportal365.formguide.service;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.match.MatchDetailEnrichService;
import com.sportal365.articlescheduler.sportsdata.sportal365.formguide.client.FormGuideApiClient;
import com.sportal365.articlescheduler.sportsdata.sportal365.formguide.mappers.FormMapper;
import com.sportal365.articlescheduler.sportsdata.sportal365.formguide.model.FormGuideResponse;
import com.sportal365.common.enums.SportEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class FormGuideService implements MatchDetailEnrichService {

    private final FormGuideApiClient formGuideApiClient;

    private final FormMapper formMapper;

    public List<FormGuideResponse> getFormGuidesForParticipants(
            List<String> participantIds, String project, SportEnum sport, String language) {

        List<FormGuideResponse> forms = new ArrayList<>();
        for(String participantId : participantIds) {
            FormGuideResponse formGuideResponse = formGuideApiClient.getTeamForm(sport.getSportValue(), participantId, project, language).block();
            forms.add(formGuideResponse);
        }
        return forms;
    }

    @Override
    public MatchDetails enrich(MatchDetails matchDetails, Schedule schedule) {
        List<FormGuideResponse> formGuideResponses = getFormGuidesForParticipants(matchDetails.getParticipantIds(), schedule.getProjectDomain(), schedule.getSport(), schedule.getLanguage());
        return matchDetails.toBuilder()
                .recentMatches(formMapper.map(formGuideResponses))
                .build();
    }
}
