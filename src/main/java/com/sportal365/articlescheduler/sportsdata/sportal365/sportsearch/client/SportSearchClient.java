package com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.infrastructure.client.contentapi.model.RelatedContentTag;
import com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.client.model.SportSearchResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.client.model.StageEvents;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriBuilder;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;

import static com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.constants.SportSearchConstants.HEADER_X_PROJECT;

@Component
@Slf4j
@AllArgsConstructor
public class SportSearchClient {

    private final WebClient sportSearchWebClient;

    public Mono<SportSearchResponse> getEventById(String eventId, String projectHeader, String translationLanguage) {
        return sportSearchWebClient.method(HttpMethod.GET)
                .uri(uriBuilder -> buildUri(uriBuilder, eventId, translationLanguage))
                .headers(headers -> configureHeaders(headers, projectHeader))
                .retrieve()
                .onStatus(HttpStatusCode::isError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(error -> Mono.error(new RuntimeException("API Error: " + error))))
                .bodyToMono(SportSearchResponse.class)
                .doOnError(error -> log.error("Error: {} fetching sport event with UUID: {}", error.getMessage(), eventId))
                .doOnSuccess(response -> log.debug("Successfully fetched sport event with UUID: {}", eventId));
    }

    public Mono<List<RelatedContentTag>> getSportConnection(MatchDetails matchDetails, String project, String translationLanguage) {
        List<RelatedContentTag> teamTags = new ArrayList<>();

        // Handle teams
        if (matchDetails.getHomeTeamId() != null) {
            teamTags.add(createTeamTag(matchDetails.getHomeTeamId()));
        }
        if (matchDetails.getAwayTeamId() != null) {
            teamTags.add(createTeamTag(matchDetails.getAwayTeamId()));
        }

        // Handle tournament
        if (matchDetails.getTournamentId() == null) {
            return Flux.fromIterable(teamTags)
                    .flatMap(tag -> fetchData(tag, project, translationLanguage))
                    .collectList();
        }

        RelatedContentTag tournamentTag = createTournamentTag(matchDetails.getTournamentId());
        teamTags.add(tournamentTag);

        return Flux.fromIterable(teamTags)
                .flatMap(tag -> fetchData(tag, project, translationLanguage))
                .collectList();
    }

    private RelatedContentTag createTeamTag(String id) {
        return RelatedContentTag.builder()
                .type("team")
                .provider("football-api")
                .data(id)
                .build();
    }

    private RelatedContentTag createTournamentTag(String id) {
        return RelatedContentTag.builder()
                .type("tournament")
                .provider("football-api")
                .data(id)
                .build();
    }

    private Mono<RelatedContentTag> fetchData(RelatedContentTag tag, String project, String translationLanguage) {
        return sportSearchWebClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/legacy/suggest")
                        .queryParam("ids", tag.getData())
                        .queryParam("translation_language", translationLanguage)
                        .build())
                .headers(headers -> configureHeaders(headers, project))
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> {
                    JsonNode results = response.get("results");
                    if (results != null && results.isArray() && !results.isEmpty()) {
                        return RelatedContentTag.builder()
                                .type(tag.getType())
                                .provider(tag.getProvider())
                                .data(results.get(0))
                                .build();
                    }
                    return tag;
                })
                .onErrorResume(error -> {
                    log.error("Error fetching data for id: {}. Error: {}", tag.getData(), error.getMessage());
                    return Mono.just(tag);
                });
    }

    private URI buildUri(UriBuilder uriBuilder, String eventId, String translationLanguage) {

        return uriBuilder
                .path("/events")
                .queryParam("ids", eventId)
                .queryParam("translation_language", translationLanguage)
                .build();
    }

    private void configureHeaders(HttpHeaders headers, String projectHeader) {
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(HEADER_X_PROJECT, projectHeader);
    }

    public Mono<StageEvents> fetchPlayoffsSchemaWithResults(
            String stageIds,
            String sport,
            String projectHeader,
            String translationLanguage
    ) {
        // Increase in-memory buffer for large playoff payloads
        ExchangeStrategies strategies = ExchangeStrategies.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(16 * 1024 * 1024))
            .build();
        WebClient client = sportSearchWebClient.mutate()
            .exchangeStrategies(strategies)
            .build();
        return client.method(HttpMethod.GET)
                .uri(uriBuilder -> uriBuilder
                        .path("/events")
                        .queryParam("stage_ids", stageIds)
                        .queryParam("sport", sport)
                        .queryParam("translation_language", translationLanguage)
                        .build())
                .headers(headers -> configureHeaders(headers, projectHeader))
                .retrieve()
                .onStatus(HttpStatusCode::isError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(error -> Mono.error(new RuntimeException("API Error: " + error))))
                .bodyToMono(StageEvents.class)
                .doOnError(error -> log.error(
                        "Error: {} fetching stage with stageIds: {}", error.getMessage(), stageIds
                ))
                .doOnSuccess(response -> log.debug(
                        "Successfully fetched stage with stageIds: {}", stageIds
                ));
    }
}
