package com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.client.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class SportSearchClientConfiguration {

    @Value("${sport.search.api.base.url}")
    private String baseUrl;

    @Bean
    public WebClient sportSearchWebClient() {
        return WebClient.builder()
                .baseUrl(baseUrl)
                .build();
    }
}
