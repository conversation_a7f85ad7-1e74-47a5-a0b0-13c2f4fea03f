package com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class StageEvents {

    private List<Results> results;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Results {
        @JsonProperty("name")
        private String matchName;

        private String sport;

        @JsonProperty("status")
        private Status status;

        private List<ParticipantResult> results;

        @JsonProperty("start_time")
        private String startTime;

        @JsonProperty("end_time")
        private String endTime;

        private Stage stage;

        private Season season;

        private Round round;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Status {
        @JsonProperty("name")
        private String statusName;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ParticipantResult {
        private String name;
        private String value;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Stage {
        private String name;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Season {
        @JsonProperty("name")
        private String seasonName;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Round {
        @JsonProperty("name")
        private String roundName;
    }
}
