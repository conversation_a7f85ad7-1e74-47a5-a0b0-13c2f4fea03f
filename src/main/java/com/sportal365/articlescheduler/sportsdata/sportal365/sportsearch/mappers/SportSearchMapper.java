package com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.mappers;

import com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.client.model.SportSearchResponse;
import com.sportal365.articlescheduler.domain.model.MatchDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class SportSearchMapper {

    public com.sportal365.articlescheduler.domain.model.MatchDetails map(SportSearchResponse response, String timeZone) {

        var firstResult = response.getResults().get(0);

        return MatchDetails.builder()
                .legacyId(firstResult.get("legacy_id").toString())
                .sport(firstResult.get("sport").toString())
                .country(getCountryName(firstResult))
                .tournamentName(getTournamentName(firstResult))
                .tournamentId(getTournamentId(firstResult))
                .season(getSeason(firstResult))
                .round(getRound(firstResult))
                .roundId(getRoundId(firstResult))
                .eventName(getEventName(firstResult))
                .date(getEventDate(firstResult))
                .time(getEventTime(firstResult, timeZone))
                .homeTeam(getTeamName(firstResult, 0))
                .awayTeam(getTeamName(firstResult, 1))
                .homeTeamId(getTeamId(firstResult, 0))
                .awayTeamId(getTeamId(firstResult, 1))
                .homeTeamLegacyId(getTeamLegacyId(firstResult, 0))
                .awayTeamLegacyId(getTeamLegacyId(firstResult, 1))
                .seasonId(extractSeasonId(firstResult))
                .stageId(extractStageId(firstResult))
                .build();
    }

    private String getCountryName(Object result) {
        return Optional.ofNullable(result)
                // First cast to Map
                .filter(Map.class::isInstance)
                .map(m -> (Map<?, ?>) m)
                // competition
                .map(m -> m.get("competition"))
                .filter(Map.class::isInstance)
                .map(m -> (Map<?, ?>) m)
                // country
                .map(m -> m.get("country"))
                .filter(Map.class::isInstance)
                .map(m -> (Map<?, ?>) m)
                // name
                .map(m -> m.get("name"))
                .map(Object::toString)
                .orElse("");
    }

    @SuppressWarnings("unchecked")
    private String getTournamentName(Object result) {
        // Step 1: Convert the top-level Object into a Map
        Optional<Map<String, Object>> resultMap = Optional.ofNullable(result)
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r);

        // Step 2: Extract "stage" from resultMap as another Map
        Optional<Map<String, Object>> stageMap = resultMap
                .map(m -> m.get("competition"))
                .filter(Map.class::isInstance)
                .map(s -> (Map<String, Object>) s);

        // Step 3: Extract the "name" field within stageMap as a String
        Optional<String> name = stageMap
                .map(m -> m.get("name"))
                .filter(String.class::isInstance)
                .map(n -> (String) n);

        // If no name is found, return the default
        return name.orElse(null);
    }

    @SuppressWarnings("unchecked")
    private String getTournamentId(Object result) {
        // Step 1: Convert the top-level Object into a Map
        Optional<Map<String, Object>> resultMap = Optional.ofNullable(result)
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r);

        // Step 2: Extract "stage" from resultMap as another Map
        Optional<Map<String, Object>> stageMap = resultMap
                .map(m -> m.get("competition"))
                .filter(Map.class::isInstance)
                .map(s -> (Map<String, Object>) s);

        // Step 3: Extract the "name" field within stageMap as a String
        Optional<String> name = stageMap
                .map(m -> m.get("id"))
                .filter(String.class::isInstance)
                .map(n -> (String) n);

        // If no name is found, return the default
        return name.orElse(null);
    }

    @SuppressWarnings("unchecked")
    private String getSeason(Object result) {
        // Step 1: Convert top-level Object to Map
        Optional<Map<String, Object>> resultMap = Optional.ofNullable(result)
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r);

        // Step 2: Extract "season" as a Map
        Optional<Map<String, Object>> seasonMap = resultMap
                .map(m -> m.get("season"))
                .filter(Map.class::isInstance)
                .map(s -> (Map<String, Object>) s);

        // Step 3: Extract "name" from the season map
        Optional<String> name = seasonMap
                .map(m -> m.get("name"))
                .filter(String.class::isInstance)
                .map(n -> (String) n);

        return name.orElse(null);
    }

    @SuppressWarnings("unchecked")
    private String getEventName(Object result) {
        // Step 1: Convert top-level Object to Map
        Optional<Map<String, Object>> resultMap = Optional.ofNullable(result)
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r);

        // Step 2: Extract "name" directly (no nested map here)
        Optional<String> name = resultMap
                .map(m -> m.get("name"))
                .filter(String.class::isInstance)
                .map(n -> (String) n);

        return name.orElse(null);
    }

    @SuppressWarnings("unchecked")
    private String getTeamName(Object result, int index) {
        // Convert the top-level Object to a Map
        Optional<Map<String, Object>> resultMap = Optional.ofNullable(result)
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r);

        // Extract the "name" field as a String
        Optional<String> nameOpt = resultMap
                .map(m -> m.get("name"))
                .filter(String.class::isInstance)
                .map(String.class::cast);

        // Split by '-' and pick the requested part (index), trimming whitespace
        return nameOpt
                .map(name -> name.split("-"))             // Split into parts
                .filter(parts -> parts.length > index)    // Ensure the array is long enough
                .map(parts -> parts[index])               // Get the indexed part
                .map(String::trim)                        // Trim whitespace
                .orElse("Unknown Team");                  // Fallback if anything fails
    }

    @SuppressWarnings("unchecked")
    private String getTeamId(Object result, int index) {
        // Convert the top-level Object to a Map
        Optional<Map<String, Object>> resultMap = Optional.ofNullable(result)
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r);

        if (resultMap.isPresent()) {
            ArrayList<Map<String, Object>> participantDetails = (ArrayList<Map<String, Object>>) resultMap.get().get("participant_details");
            if (participantDetails != null && !participantDetails.isEmpty()) {
                Map<String, Object> participant = participantDetails.get(index);
                if (participant != null) {
                    Map<String, Object> team = (Map<String, Object>) participant.get("participant");
                    if (team != null) {
                        return (String) team.get("id");
                    }
                }
            }
        }

        return null;
    }

    @SuppressWarnings("unchecked")
    private String getTeamLegacyId(Object result, int index) {
        // Convert the top-level Object to a Map
        Optional<Map<String, Object>> resultMap = Optional.ofNullable(result)
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r);

        if (resultMap.isPresent()) {
            ArrayList<Map<String, Object>> participantDetails = (ArrayList<Map<String, Object>>) resultMap.get().get("participant_details");
            if (participantDetails != null && !participantDetails.isEmpty()) {
                Map<String, Object> participant = participantDetails.get(index);
                if (participant != null) {
                    Map<String, Object> team = (Map<String, Object>) participant.get("participant");
                    if (team != null) {
                        return (String) team.get("legacy_id");
                    }
                }
            }
        }

        return null;
    }

    @SuppressWarnings("unchecked")
    private String getEventDate(Object result) {
        // Step 1: Convert top-level Object to Map, then get "start_time" as a String
        Optional<String> dateTimeStringOpt = Optional.ofNullable(result)
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r)
                .map(r -> r.get("start_time"))
                .filter(String.class::isInstance)
                .map(s -> (String) s);

        // Step 2: Parse the string as a ZonedDateTime and format it, or null if parsing fails
        return dateTimeStringOpt.map(dateTimeString -> {
            try {
                ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateTimeString);
                return zonedDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                // Could log here if needed
                return null; // or "Unknown Date" if you prefer a default
            }
        }).orElse(null);
    }

    @SuppressWarnings("unchecked")
    private String getEventTime(Object result, String timeZone) {
        // Step 1: Convert top-level Object to Map, then get "start_time" as a String
        Optional<String> dateTimeStringOpt = Optional.ofNullable(result)
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r)
                .map(r -> r.get("start_time"))
                .filter(String.class::isInstance)
                .map(s -> (String) s);

        // Step 2: Parse the string, convert to the specified timezone, and format it
        return dateTimeStringOpt.map(dateTimeString -> {
            try {
                // Parse the date string to an Instant (keeps it in UTC)
                Instant instant = Instant.parse(dateTimeString);

                // Convert to the specified timezone
                ZonedDateTime zonedDateTime = instant.atZone(ZoneId.of(timeZone));

                // Format the time in the specified timezone
                return zonedDateTime.format(DateTimeFormatter.ofPattern("HH:mm"));
            } catch (Exception e) {
                // Log an error if desired
                // log.error("Failed to parse event date and time: {}", dateTimeString, e);
                return null; // or "Unknown Time"
            }
        }).orElse(null);
    }

    @SuppressWarnings("unchecked")
    private String getRound(Object result) {
        return Optional.ofNullable(result)
                // 1. Verify and cast top-level object to Map
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r)
                // 2. Get "round" as a nested Map
                .map(map -> map.get("round"))
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r)
                // 3. Get "name" from the round map
                .map(roundMap -> roundMap.get("name"))
                .filter(String.class::isInstance)
                .map(String.class::cast)
                // 4. Fallback if missing or invalid
                .orElse(null);
    }

    @SuppressWarnings("unchecked")
    private String getRoundId(Object result) {
        return Optional.ofNullable(result)
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r)
                .map(m -> m.get("round"))
                .filter(Map.class::isInstance)
                .map(r -> (Map<String, Object>) r)
                .map(roundMap -> roundMap.get("id"))
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .orElse(null);
    }

    private String extractSeasonId(Map<String, Object> searchResult) {
        return Optional.ofNullable(searchResult)
                        .map(result -> result.get("season"))
                .filter(season -> season instanceof Map<?, ?>)
                .map(season -> (Map<?, ?>) season)
                .map(seasonMap -> seasonMap.get("id"))
                .filter(id -> id instanceof String)
                .map(String.class::cast)
                .orElse(null);
    }

    private String extractStageId(Map<String, Object> searchResult) {
        return Optional.ofNullable(searchResult)
                .map(result -> result.get("stage"))
                .filter(season -> season instanceof Map<?, ?>)
                .map(season -> (Map<?, ?>) season)
                .map(seasonMap -> seasonMap.get("id"))
                .filter(id -> id instanceof String)
                .map(String.class::cast)
                .orElse(null);
    }

}
