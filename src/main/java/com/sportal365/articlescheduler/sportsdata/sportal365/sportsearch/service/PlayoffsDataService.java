package com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.service;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.match.MatchDetailEnrichService;
import com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.client.SportSearchClient;
import com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.client.model.StageEvents;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class PlayoffsDataService implements MatchDetailEnrichService {

    private final SportSearchClient sportSearchClient;

    @Override
    public MatchDetails enrich(MatchDetails matchDetails, Schedule schedule) {

        if (matchDetails.getStandings() == null || matchDetails.getStandings().isPlayoffs()) {

            return getPlayoffsData(
                    matchDetails, schedule.getSport().getSportValue(), schedule.getProjectDomain(), schedule.getLanguage());
        }

        return matchDetails;
    }

    private MatchDetails getPlayoffsData(
            MatchDetails matchDetails, String sport, String project, String translationLanguage) {

        StageEvents stageEvents = sportSearchClient.fetchPlayoffsSchemaWithResults(
                matchDetails.getStageId(), sport, project, translationLanguage).block();

        List<StageEvents.Results> resultsList = Optional.ofNullable(stageEvents)
                .map(StageEvents::getResults)
                .orElse(Collections.emptyList());
        if (resultsList.isEmpty()) {
            return matchDetails;
        }
        MatchDetails.PlayoffsData playoffsData = MatchDetails.PlayoffsData.builder()
                .results(resultsList)
                .build();

        matchDetails.setPlayoffsData(playoffsData);

        return matchDetails;
    }
}
