package com.sportal365.articlescheduler.sportsdata.sportal365.standings.client;

import com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.model.StandingsResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriBuilder;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.function.Function;

import static com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.constants.SportSearchConstants.HEADER_X_PROJECT;
import static com.sportal365.articlescheduler.sportsdata.sportal365.standings.constants.StandingsConstants.*;

@Slf4j
@RequiredArgsConstructor
@Component
public class StandingsApiClient {

    private final WebClient standingsApiWebClient;

    public Mono<StandingsResponse> getStandingsByStageId(String sport, String stageId) {
        return standingsApiWebClient
                .get()
                .uri(buildStandingsUri(sport, null, stageId))
                .headers(this::configureHeaders)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(StandingsResponse.class)
                .doOnSubscribe(subscription -> log.debug("Fetching standings for sport: {}, stageId: {}",
                        sport, stageId))
                .doOnSuccess(response -> {
                    if (response != null && response.getData() != null && !response.getData().isEmpty()) {
                        log.debug("Successfully fetched standings with data");
                    } else {
                        log.debug("Successfully fetched standings but data list is empty");
                    }
                })
                .flatMap(response -> {
                    // Check if response data is empty or null
                    if (response == null || response.getData() == null || response.getData().isEmpty()) {
                        log.debug("Empty data returned for stageId: {}, returning empty Mono", stageId);
                        return Mono.empty();
                    }
                    return Mono.just(response);
                })
                .onErrorResume(error -> {
                    log.error("Error fetching standings: {}", error.getMessage());
                    return Mono.empty();
                });
    }

    public Mono<StandingsResponse> getStandingsBySeasonId(String sport, String seasonId) {

        return standingsApiWebClient
                .get()
                .uri(buildStandingsUri(sport, seasonId, null))
                .headers(this::configureHeaders)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(StandingsResponse.class)
                .doOnSubscribe(subscription -> log.debug("Fetching standings for sport: {}, seasonId: {}",
                        sport, seasonId))
                .doOnSuccess(response -> log.debug("Successfully fetched standings"))
                .doOnError(error -> log.error("Error fetching standings: {}", error.getMessage()));
    }

    private Function<UriBuilder, URI> buildStandingsUri(String sport, String seasonId, String stageId) {

        return uriBuilder -> {
            determineIdentifier(seasonId, stageId, uriBuilder.path(PATH_STANDINGS));
            return uriBuilder.build(sport);
        };
    }
    private void validateInput(String seasonId, String stageId) {
        if (StringUtils.hasText(seasonId) && StringUtils.hasText(stageId)) {
            throw new IllegalArgumentException(String.format("Both seasonId and stageId provided. Using seasonId: " +
                    "%s and ignoring stageId: %s", seasonId, stageId));
        }

        if (!StringUtils.hasText(seasonId) && !StringUtils.hasText(stageId)) {
            throw new IllegalArgumentException("Either seasonId or stageId must be provided");
        }
    }

    private void determineIdentifier(String seasonId, String stageId, UriBuilder builder) {
        if (StringUtils.hasText(seasonId)) {
            builder.queryParam(QUERY_PARAM_SEASON_ID, seasonId);
        } else {
            builder.queryParam(QUERY_PARAM_STAGE_ID, stageId);
        }
    }

    private void configureHeaders(HttpHeaders headers) {
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(HEADER_X_PROJECT, "sportal365");
    }
}
