package com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.configuration;

import com.sportal365.articlescheduler.infrastructure.client.common.AbstractApiClientConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class StandingsApiConfiguration extends AbstractApiClientConfiguration {

    @Value("${standings.api.base.url}")
    private String standingsApiBaseUrl;

    @Value("${standings.api.username}")
    private String username;

    @Value("${standings.api.password}")
    private String password;

    @Bean
    public WebClient standingsApiWebClient() {
        return createWebClient(standingsApiBaseUrl, username, password);
    }
}
