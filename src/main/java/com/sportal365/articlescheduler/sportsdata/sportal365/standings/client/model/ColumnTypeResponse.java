package com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ColumnTypeResponse extends BaseEntityResponse {

    private String name;

    private String code;

    @JsonProperty("entity_type")
    private String entityType;

    @JsonProperty("short_name")
    private String shortName;
}
