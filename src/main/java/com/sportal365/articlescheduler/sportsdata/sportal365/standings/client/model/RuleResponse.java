package com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class RuleResponse extends BaseEntityResponse {

    private String name;

    private String type;

    @JsonProperty("standing_api_rule_type")
    private String standingApiRule;

    private String standingApiRuleType;

    @JsonProperty("entity_type")
    private String entityType;
}