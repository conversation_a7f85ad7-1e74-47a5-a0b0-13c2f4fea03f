package com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class StandingGroup {

    @JsonProperty("group")
    private GroupResponse groupResponse;

    private String type; // "FINISHED", "LIVE", etc.

    private String filter;

    @JsonProperty("ranking_type")
    private String rankingType;

    @JsonProperty("standing")
    private List<StandingResponse> standingResponse;
}
