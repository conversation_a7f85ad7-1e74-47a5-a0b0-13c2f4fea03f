package com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class StandingResponse {

    @JsonProperty("team")
    private TeamResponse teamResponse;

    @JsonProperty("rules")
    private List<RuleResponse> ruleResponses;

    @JsonProperty("columns")
    private List<ColumnResponse> columnResponses;


    private List<String> form;
}