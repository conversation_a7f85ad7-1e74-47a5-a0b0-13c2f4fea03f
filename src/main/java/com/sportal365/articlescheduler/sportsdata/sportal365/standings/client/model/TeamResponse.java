package com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class TeamResponse extends BaseEntityResponse {

    private String id;

    private String name;

    private String type;

    @JsonProperty("entity_type")
    private String entityType;

    private String gender;

    @JsonProperty("three_letter_code")
    private String threeLetterCode;
}
