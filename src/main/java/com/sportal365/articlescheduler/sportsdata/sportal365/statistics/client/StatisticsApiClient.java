package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client;

import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticsApiAggregateResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticsApiEventsResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriBuilder;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.List;
import java.util.function.Function;

import static com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.constants.SportSearchConstants.HEADER_X_PROJECT;
import static com.sportal365.articlescheduler.sportsdata.sportal365.statistics.constants.StatisticsConstants.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class StatisticsApiClient {

    private final WebClient statisticsApiWebClient;

    public Mono<StatisticsApiAggregateResponse> getParticipantSeasonStatistics(String projectHeader,
                                                                               String participantId,
                                                                               String seasonId) {
        validateRequiredParameter("X-Project header", projectHeader);
        validateRequiredParameter(QUERY_PARAM_PARTICIPANTS, participantId);
        validateRequiredParameter(QUERY_PARAM_SEASONS, seasonId);

        return statisticsApiWebClient
                .get()
                .uri(uriBuilder -> {
                    UriBuilder builder = uriBuilder
                            .path(AGGREGATE_STATISTICS_ENDPOINT)
                            .queryParam(QUERY_PARAM_PARTICIPANTS, participantId)
                            .queryParam(QUERY_PARAM_SEASONS, seasonId);

                    return builder.build();
                })
                .headers(headers -> configureHeaders(headers, projectHeader))
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(StatisticsApiAggregateResponse.class)
                .doOnSubscribe(subscription -> log.debug("Fetching aggregate data for participant {} in season {}", participantId, seasonId))
                .doOnSuccess(response -> log.debug(CLIENT_LOG_MESSAGE_SUCCESS))
                .doOnError(error -> log.error(CLIENT_LOG_MESSAGE_FAILURE, error.getMessage()));
    }

    public Mono<StatisticsApiEventsResponse> getParticipantSeasonStatisticsDiscrete(String projectHeader,
                                                                                    String participantId,
                                                                                    String seasonId) {
        validateRequiredParameter("X-Project header", projectHeader);
        validateRequiredParameter(QUERY_PARAM_PARTICIPANTS, participantId);
        validateRequiredParameter(QUERY_PARAM_SEASONS, seasonId);

        return fetchDiscreteStatisticsData(
                projectHeader,
                uriBuilder -> uriBuilder
                        .path(EVENT_STATISTICS_ENDPOINT)
                        .queryParam(QUERY_PARAM_PARTICIPANTS, participantId)
                        .queryParam(QUERY_PARAM_SEASONS, seasonId)
                        .build(),
                "participant " + participantId + " in season " + seasonId
        );
    }

    public Mono<StatisticsApiEventsResponse> getParticipantStatisticsDiscrete(String projectHeader,
                                                                              String participantId,
                                                                              List<String> eventIds) {
        validateRequiredParameter("X-Project header", projectHeader);
        validateRequiredParameter(QUERY_PARAM_PARTICIPANTS, participantId);
        validateRequiredParameter(QUERY_PARAM_EVENTS, eventIds);

        return fetchDiscreteStatisticsData(
                projectHeader,
                uriBuilder -> uriBuilder
                        .path(EVENT_STATISTICS_ENDPOINT)
                        .queryParam(QUERY_PARAM_PARTICIPANTS, participantId)
                        .queryParam(QUERY_PARAM_EVENTS, eventIds)
                        .build(),
                "participant " + participantId + " for events " + eventIds
        );
    }

    private Mono<StatisticsApiEventsResponse> fetchDiscreteStatisticsData(
            String projectHeader,
            Function<UriBuilder, URI> uriBuilderFunction,
            String logMessage) {

        return statisticsApiWebClient
                .get()
                .uri(uriBuilderFunction)
                .headers(headers -> configureHeaders(headers, projectHeader))
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(StatisticsApiEventsResponse.class)
                .doOnSubscribe(subscription -> log.debug("Fetching statistics data for {}", logMessage))
                .doOnSuccess(response -> log.debug(CLIENT_LOG_MESSAGE_SUCCESS))
                .doOnError(error -> log.error(CLIENT_LOG_MESSAGE_FAILURE, error.getMessage()));
    }

    private void validateRequiredParameter(String parameterName, String parameterValue) {
        if (parameterValue == null || parameterValue.isEmpty()) {
            throw new IllegalArgumentException(parameterName + " must not be null or empty");
        }
    }

    private void validateRequiredParameter(String parameterName, List<String> parameterValues) {
        if (parameterValues == null || parameterValues.isEmpty()) {
            throw new IllegalArgumentException(parameterName + " must not be null or empty");
        }
    }

    private void configureHeaders(HttpHeaders headers, String projectHeader) {
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(HEADER_X_PROJECT, projectHeader);
    }

}
