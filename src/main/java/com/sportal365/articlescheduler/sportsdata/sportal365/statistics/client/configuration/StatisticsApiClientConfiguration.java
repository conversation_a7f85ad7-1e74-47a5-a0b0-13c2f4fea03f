package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.configuration;

import com.sportal365.articlescheduler.infrastructure.client.common.AbstractApiClientConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class StatisticsApiClientConfiguration extends AbstractApiClientConfiguration {

    @Value("${statistics.api.base.url}")
    private String staticsApiBaseUrl;

    @Value("${football.api.username}")
    private String username;

    @Value("${football.api.password}")
    private String password;

    @Bean
    public WebClient statisticsApiWebClient() {
        return WebClient.builder()
                .baseUrl(staticsApiBaseUrl)
                .defaultHeaders(headers -> headers.setBasicAuth(username, password))
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(1024 * 1024)) // 1MB buffer for Stats API responses
                .build();
    }

}
