package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.util.StatisticsWhitelist;
import lombok.Data;

import java.util.List;

@Data
public class ParticipantDto {
    @JsonProperty("id")
    private String id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("entity_type")
    private String entityType;
    @JsonProperty("gender")
    private String gender;
    @JsonProperty("statistics")
    private List<StatisticDto> statistics;

    public List<StatisticDto> getStatistics() {
        if (statistics == null) {
            return List.of();
        }

        return statistics.stream().filter(statisticDto -> StatisticsWhitelist.containsId(statisticDto.getId())).toList();
    }
}
