package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.wrappers;

import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.ParticipantDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticsApiEventsResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public class StarResponsesWrapper {
    private String teamName;
    private ParticipantDto participantWithAggregateStatistics;
    private StatisticsApiEventsResponse participantLatestEventsResponse;
    private StatisticsApiEventsResponse participantLatestVersusEventResponse;
}
