package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.constants;

public class StatisticsConstants {
    public static final String ENTITY_TYPE_PLAYER = "player";

    public static final String POSITION_MIDFIELDER = "MIDFIELDER";
    public static final String POSITION_FORWARD = "FORWARD";

    public static final String LINEUP_TYPE_INJURED = "Injured";
    public static final String LINEUP_TYPE_SUSPENDED = "Suspended";
    public static final String LINEUP_TYPE_UNKNOWN = "Unknown";

    public static final String STATISTIC_STARTED_ID = "a3f81922-13dd-4988-bc91-812a68681482";
    public static final String STATISTIC_RATING_AVG_ID = "6c324825-0b0c-4788-8812-df51b65d0b51";

    public static final int MINIMUM_STARTED_EVENTS = 3;

    public static final String EVENT_STATISTICS_ENDPOINT = "/statistics";
    public static final String AGGREGATE_STATISTICS_ENDPOINT = EVENT_STATISTICS_ENDPOINT + "/aggregate";

    public static final String QUERY_PARAM_PARTICIPANTS = "participant_ids";
    public static final String QUERY_PARAM_SEASONS = "season_ids";
    public static final String QUERY_PARAM_EVENTS = "event_ids";

    public static final String CLIENT_LOG_MESSAGE_SUCCESS = "Successfully fetched statistics data";
    public static final String CLIENT_LOG_MESSAGE_FAILURE = "Error fetching statistics data: {}";
}
