package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.util;

import lombok.Builder;
import lombok.Data;

/**
 * Abstraction class used to handle player statistics regardless of the source of players:
 * a) team lineups if such are available
 * b) team squads if lineups are not available
 */
@Data
@Builder
public class StatisticsPlayer {
    private String name;
    private String lineupStatus;
    private String position;
    private boolean active;
}
