package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.util;

import java.util.Arrays;

public enum StatisticsWhitelist {
    ASSISTS("e666f4a4-a496-4432-b815-2a2c9bacca52"),
    EXPECTED_GOAL("eca6824c-631e-43ab-b6bc-e66005840fbb"),
    GO<PERSON><PERSON>("36c1b27a-f84a-4351-a9f4-1c23a346076d"),
    GOALS_AS_SUBSTITUTE("6d8c15de-a048-47f0-8616-08e8044e2faf"),
    MINUTES_AS_SUBSTITUTE("26d0ee0b-681d-4fbc-bb47-81c6b83ec8a5"),
    MINUTES_PLAYED("e63aa3d2-4177-4927-bf6b-95a43f6f07cb"),
    MISSED_PENALTIES("a98ab2eb-9518-4777-a1d6-c7261c06dfd8"),
    OWN_GOALS("84f4e772-63df-4d8d-91f3-f103511bd1f7"),
    PENALTIES_SCORED("f5d2502a-5001-4e47-bd33-c0a82c1cfee0"),
    PENALTIES_COMMITTED("9b758548-68cc-4e44-afc4-3068e342299c"),
    EVENTS_PLAYED("9c474c5c-fdbe-47df-b438-8e2d494e8083"),
    RATING("6c324825-0b0c-4788-8812-df51b65d0b51"),
    RED_CARDS("5ebb9fd8-fc6c-4f1c-9db2-ef541d1a993c"),
    SHOTS("fd1d71fa-7f1f-489b-996b-8ae085216dba"),
    SHOTS_ON_TARGET("da11a5b2-7533-40c7-9c91-2e2655f1d9a5"),
    EVENTS_STARTED("a3f81922-13dd-4988-bc91-812a68681482"),
    SUCCESSFUL_CROSSES("bbb84312-5da4-490d-8d5a-1b8067c5df68"),
    YELLOW_CARDS("1b17cadb-d7f4-4587-8605-4d5506e5399b");

    private final String id;

    StatisticsWhitelist(String id) {
        this.id = id;
    }

    public static boolean containsId(String searchId) {
        return Arrays.stream(values())
                .anyMatch(stat -> stat.id.equals(searchId));
    }
}
