application.title=articlescheduler

# MongoDB Configuration
spring.data.mongodb.uri=${MONGO_DB_URI}?authSource=admin
spring.data.mongodb.database=${MONGO_DB_NAME}
spring.data.mongodb.auto-index-creation=true

# Scheduler Configuration
schedule.processing.cron=${SCHEDULER_CRON_EXPRESSION}
retry.cron.expression=0 */30 * * * *

#configuration client
client.api.base.url=${CLIENT_API_BASE_URL}
client.api.username=${CLIENT_API_USERNAME}
client.api.password=${CLIENT_API_PASSWORD}
spring.rabbitmq.config.client.queue=${RABBITMQ_CONFIG_CLIENT_QUEUE}
spring.rabbitmq.config.client.virtual-host=${RABBITMQ_CONFIG_CLIENT_VHOST}
spring.rabbitmq.config.client.template.exchange=${RABBITMQ_CONFIG_CLIENT_EXCHANGE}
spring.rabbitmq.config.client.project.queue=${RABBITMQ_CONFIG_CLIENT_PROJECT_QUEUE}
spring.rabbitmq.config.client.template.project.exchange=${RABBITMQ_CONFIG_CLIENT_PROJECT_EXCHANGE}
spring.rabbitmq.config.client.template.project.routing.key=${RABBITMQ_CONFIG_CLIENT_PROJECT_ROUTING_KEY}

# RabbitMQ
spring.rabbitmq.host=${RABBITMQ_HOST}
spring.rabbitmq.port=${RABBITMQ_PORT}
spring.rabbitmq.username=${RABBITMQ_USERNAME}
spring.rabbitmq.password=${RABBITMQ_PASSWORD}

#Migrations
migration.schedule-ids.enabled=${MIGRATION_SCHEDULE_IDS_ENABLED:false}

# Default project
default.template.project=${DEFAULT_TEMPLATE_PROJECT}

# Football API Configuration
football.api.base.url=${FOOTBALL_API_BASE_URL}
football.api.username=${FOOTBALL_API_USERNAME}
football.api.password=${FOOTBALL_API_PASSWORD}

# Search API
sport.search.api.base.url=${SEARCH_API_BASE_URL}

#Standings API
standings.api.base.url=${STANDINGS_API_BASE_URL}
standings.api.username=${STANDINGS_API_USERNAME}
standings.api.password=${STANDINGS_API_PASSWORD}

#Form Guide API
form.guide.api.base.url=${FORM_GUIDE_API_BASE_URL}

#Sports Statistics API
statistics.api.base.url=${STATISTICS_API_BASE_URL}


#Content API
content.api.base.url=${CONTENT_API_BASE_URL}
content.api.username=${CONTENT_API_USERNAME}
content.api.password=${CONTENT_API_PASSWORD}
content.api.client-id=${CONTENT_API_CLIENT_ID}
content.api.client-secret=${CONTENT_API_CLIENT_SECRET}

#Widget Blocky API
widget.blocky.service.api.base.url=${WIDGET_BLOCKY_API_BASE_URL}

#LLM Service
llm.service.api.base.url=${LLM_SERVICE}
