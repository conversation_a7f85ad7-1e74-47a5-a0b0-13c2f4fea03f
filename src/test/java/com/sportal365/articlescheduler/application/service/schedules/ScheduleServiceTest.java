package com.sportal365.articlescheduler.application.service.schedules;

import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleCreateRequest;
import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleMatchRequest;
import com.sportal365.articlescheduler.domain.model.enums.ScheduleType;
import com.sportal365.articlescheduler.domain.validator.ScheduleRequestValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ScheduleService Tests")
class ScheduleServiceTest {

    private ScheduleRequestValidator validator;

    @BeforeEach
    void setUp() {
        validator = new ScheduleRequestValidator();
    }

    @Nested
    @DisplayName("ScheduleType Enum Tests")
    class ScheduleTypeEnumTests {

        @Test
        @DisplayName("Should verify ScheduleType enum has both IMMEDIATELY and SCHEDULED values")
        void shouldVerifyScheduleTypeEnumHasBothValues() {
            // Assert
            assertNotNull(ScheduleType.IMMEDIATELY);
            assertNotNull(ScheduleType.SCHEDULED);
            assertEquals("IMMEDIATELY", ScheduleType.IMMEDIATELY.name());
            assertEquals("SCHEDULED", ScheduleType.SCHEDULED.name());
        }

        @Test
        @DisplayName("Should verify ScheduleType default is IMMEDIATELY")
        void shouldVerifyScheduleTypeDefaultIsImmediately() {
            // Assert
            assertEquals(ScheduleType.IMMEDIATELY, ScheduleType.getDefault());
        }

        @Test
        @DisplayName("Should verify ScheduleType.fromString works correctly")
        void shouldVerifyScheduleTypeFromStringWorksCorrectly() {
            // Test valid input for IMMEDIATELY
            assertEquals(ScheduleType.IMMEDIATELY, ScheduleType.fromString("immediately"));
            assertEquals(ScheduleType.IMMEDIATELY, ScheduleType.fromString("IMMEDIATELY"));

            // Test valid input for SCHEDULED
            assertEquals(ScheduleType.SCHEDULED, ScheduleType.fromString("scheduled"));
            assertEquals(ScheduleType.SCHEDULED, ScheduleType.fromString("SCHEDULED"));

            // Test invalid input
            assertNull(ScheduleType.fromString("invalid"));
            assertNull(ScheduleType.fromString(null));
        }

        @Test
        @DisplayName("Should verify all ScheduleType values are supported")
        void shouldVerifyAllScheduleTypeValuesAreSupported() {
            // Test that we have exactly 2 enum values
            ScheduleType[] values = ScheduleType.values();
            assertEquals(2, values.length);

            // Test that both expected values exist
            assertTrue(java.util.Arrays.asList(values).contains(ScheduleType.IMMEDIATELY));
            assertTrue(java.util.Arrays.asList(values).contains(ScheduleType.SCHEDULED));
        }
    }

    @Nested
    @DisplayName("Schedule Creation with schedule_type Tests")
    class ScheduleCreationTests {

        @Test
        @DisplayName("Should create request with IMMEDIATELY schedule type")
        void shouldCreateRequestWithImmediatelyScheduleType() {
            // Arrange & Act
            ScheduleCreateRequest request = createRequestWithScheduleType(ScheduleType.IMMEDIATELY);

            // Assert
            assertNotNull(request);
            assertEquals(ScheduleType.IMMEDIATELY, request.getScheduleType());
            assertEquals("FOOTBALL", request.getSport());
            assertEquals("Match Preview team news and quotes", request.getTemplateName());
        }

        @Test
        @DisplayName("Should create request with SCHEDULED schedule type")
        void shouldCreateRequestWithScheduledScheduleType() {
            // Arrange & Act
            ScheduleCreateRequest request = createRequestWithScheduleType(ScheduleType.SCHEDULED);

            // Assert
            assertNotNull(request);
            assertEquals(ScheduleType.SCHEDULED, request.getScheduleType());
            assertEquals("FOOTBALL", request.getSport());
        }

        @Test
        @DisplayName("Should create request without schedule type")
        void shouldCreateRequestWithoutScheduleType() {
            // Arrange & Act
            ScheduleCreateRequest request = createRequestWithoutScheduleType();

            // Assert
            assertNotNull(request);
            assertNull(request.getScheduleType()); // Should be null when not provided
            assertEquals("FOOTBALL", request.getSport());
        }
    }

    @Nested
    @DisplayName("Validation Tests")
    class ValidationTests {

        @Test
        @DisplayName("Should accept valid schedule_type values")
        void shouldAcceptValidScheduleTypeValues() {
            // Test IMMEDIATELY
            ScheduleCreateRequest immediateRequest = createRequestWithScheduleType(ScheduleType.IMMEDIATELY);
            assertDoesNotThrow(() -> validator.validate(immediateRequest));

            // Test SCHEDULED
            ScheduleCreateRequest scheduledRequest = createRequestWithScheduleType(ScheduleType.SCHEDULED);
            assertDoesNotThrow(() -> validator.validate(scheduledRequest));

            // Test without schedule_type (should be valid)
            ScheduleCreateRequest noTypeRequest = createRequestWithoutScheduleType();
            assertDoesNotThrow(() -> validator.validate(noTypeRequest));
        }

        @Test
        @DisplayName("Should create multiple matches request")
        void shouldCreateMultipleMatchesRequest() {
            // Arrange & Act
            ScheduleCreateRequest request = createRequestWithMultipleMatches(ScheduleType.IMMEDIATELY);

            // Assert
            assertNotNull(request);
            assertEquals(ScheduleType.IMMEDIATELY, request.getScheduleType());
            assertEquals(2, request.getMatches().size());
            assertEquals("Челси - ФК Лос Анджелис", request.getMatches().get(0).getMatchName());
            assertEquals("Реал Мадрид - Манчестър Сити", request.getMatches().get(1).getMatchName());
        }
    }

    // Helper methods for creating test data
    private ScheduleCreateRequest createRequestWithScheduleType(ScheduleType scheduleType) {
        return ScheduleCreateRequest.builder()
                .templateName("Match Preview team news and quotes")
                .templateType("PRE_GAME")
                .category("2025011511551414715")
                .categoryName("test-propертъ")
                .userId("2018060808173367773")
                .userName("Service Admin")
                .sport("FOOTBALL")
                .generateSummary(true)
                .generateStrapline(true)
                .scheduleType(scheduleType)
                .matches(List.of(createMockMatchRequest()))
                .build();
    }

    private ScheduleCreateRequest createRequestWithoutScheduleType() {
        return ScheduleCreateRequest.builder()
                .templateName("Match Preview team news and quotes")
                .templateType("PRE_GAME")
                .category("2025011511551414715")
                .categoryName("test-propертъ")
                .userId("2018060808173367773")
                .userName("Service Admin")
                .sport("FOOTBALL")
                .generateSummary(true)
                .generateStrapline(true)
                // No scheduleType field - should default to IMMEDIATELY
                .matches(List.of(createMockMatchRequest()))
                .build();
    }

    private ScheduleCreateRequest createRequestWithMultipleMatches(ScheduleType scheduleType) {
        return ScheduleCreateRequest.builder()
                .templateName("Match Preview team news and quotes")
                .templateType("PRE_GAME")
                .category("2025011511551414715")
                .categoryName("test-propертъ")
                .userId("2018060808173367773")
                .userName("Service Admin")
                .sport("FOOTBALL")
                .generateSummary(true)
                .generateStrapline(true)
                .scheduleType(scheduleType)
                .matches(List.of(
                    createMockMatchRequest(),
                    createSecondMockMatchRequest()
                ))
                .build();
    }

    private ScheduleMatchRequest createMockMatchRequest() {
        return ScheduleMatchRequest.builder()
                .matchId("c9ad762a-09da-4a40-89b3-f8a5483cb293")
                .competitionId("92c3e74c-665b-4251-8ddb-d3bb042140f3")
                .competitionName("Световно клубно първенство")
                .matchName("Челси - ФК Лос Анджелис")
                .matchDate(Instant.parse("2025-06-18T19:00:00.000Z"))
                .build();
    }

    private ScheduleMatchRequest createSecondMockMatchRequest() {
        return ScheduleMatchRequest.builder()
                .matchId("d8be873b-10eb-5b51-9ac4-g9b6594dc394")
                .competitionId("92c3e74c-665b-4251-8ddb-d3bb042140f3")
                .competitionName("Световно клубно първенство")
                .matchName("Реал Мадрид - Манчестър Сити")
                .matchDate(Instant.parse("2025-06-19T20:00:00.000Z"))
                .build();
    }


}
