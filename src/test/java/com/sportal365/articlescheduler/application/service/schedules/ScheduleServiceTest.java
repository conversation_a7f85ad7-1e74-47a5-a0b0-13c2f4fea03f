package com.sportal365.articlescheduler.application.service.schedules;

import com.sportal365.articlescheduler.domain.model.enums.ScheduleType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ScheduleService Tests")
class ScheduleServiceTest {

    @Test
    @DisplayName("Should verify ScheduleType enum has both IMMEDIATELY and SCHEDULED values")
    void shouldVerifyScheduleTypeEnumHasBothValues() {
        // Assert
        assertNotNull(ScheduleType.IMMEDIATELY);
        assertNotNull(ScheduleType.SCHEDULED);
        assertEquals("IMMEDIATELY", ScheduleType.IMMEDIATELY.name());
        assertEquals("SCHEDULED", ScheduleType.SCHEDULED.name());
    }

    @Test
    @DisplayName("Should verify ScheduleType default is IMMEDIATELY")
    void shouldVerifyScheduleTypeDefaultIsImmediately() {
        // Assert
        assertEquals(ScheduleType.IMMEDIATELY, ScheduleType.getDefault());
    }

    @Test
    @DisplayName("Should verify ScheduleType.fromString works correctly")
    void shouldVerifyScheduleTypeFromStringWorksCorrectly() {
        // Test valid input for IMMEDIATELY
        assertEquals(ScheduleType.IMMEDIATELY, ScheduleType.fromString("immediately"));
        assertEquals(ScheduleType.IMMEDIATELY, ScheduleType.fromString("IMMEDIATELY"));

        // Test valid input for SCHEDULED
        assertEquals(ScheduleType.SCHEDULED, ScheduleType.fromString("scheduled"));
        assertEquals(ScheduleType.SCHEDULED, ScheduleType.fromString("SCHEDULED"));

        // Test invalid input
        assertNull(ScheduleType.fromString("invalid"));
        assertNull(ScheduleType.fromString(null));
    }

    @Test
    @DisplayName("Should verify all ScheduleType values are supported")
    void shouldVerifyAllScheduleTypeValuesAreSupported() {
        // Test that we have exactly 2 enum values
        ScheduleType[] values = ScheduleType.values();
        assertEquals(2, values.length);

        // Test that both expected values exist
        assertTrue(java.util.Arrays.asList(values).contains(ScheduleType.IMMEDIATELY));
        assertTrue(java.util.Arrays.asList(values).contains(ScheduleType.SCHEDULED));
    }

}
