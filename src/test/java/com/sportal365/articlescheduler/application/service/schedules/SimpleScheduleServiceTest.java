package com.sportal365.articlescheduler.application.service.schedules;

import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleCreateRequest;
import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleMatchRequest;
import com.sportal365.articlescheduler.domain.model.enums.ScheduleType;
import com.sportal365.articlescheduler.domain.validator.ScheduleRequestValidator;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Simple ScheduleService Tests")
class SimpleScheduleServiceTest {

    private final ScheduleRequestValidator validator = new ScheduleRequestValidator();

    @Test
    @DisplayName("Should verify ScheduleType enum functionality")
    void shouldVerifyScheduleTypeEnumFunctionality() {
        // Test enum values exist
        assertNotNull(ScheduleType.IMMEDIATELY);
        assertNotNull(ScheduleType.SCHEDULED);
        
        // Test default
        assertEquals(ScheduleType.IMMEDIATELY, ScheduleType.getDefault());
        
        // Test fromString
        assertEquals(ScheduleType.IMMEDIATELY, ScheduleType.fromString("immediately"));
        assertEquals(ScheduleType.SCHEDULED, ScheduleType.fromString("scheduled"));
        assertNull(ScheduleType.fromString("invalid"));
    }

    @Test
    @DisplayName("Should create valid request with IMMEDIATELY schedule type")
    void shouldCreateValidRequestWithImmediatelyScheduleType() {
        // Arrange & Act
        ScheduleCreateRequest request = createTestRequest(ScheduleType.IMMEDIATELY);

        // Assert
        assertNotNull(request);
        assertEquals(ScheduleType.IMMEDIATELY, request.getScheduleType());
        assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    @DisplayName("Should create valid request with SCHEDULED schedule type")
    void shouldCreateValidRequestWithScheduledScheduleType() {
        // Arrange & Act
        ScheduleCreateRequest request = createTestRequest(ScheduleType.SCHEDULED);

        // Assert
        assertNotNull(request);
        assertEquals(ScheduleType.SCHEDULED, request.getScheduleType());
        assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    @DisplayName("Should create valid request without schedule type")
    void shouldCreateValidRequestWithoutScheduleType() {
        // Arrange & Act
        ScheduleCreateRequest request = createTestRequest(null);

        // Assert
        assertNotNull(request);
        assertNull(request.getScheduleType());
        assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    @DisplayName("Should validate request based on example curl")
    void shouldValidateRequestBasedOnExampleCurl() {
        // Arrange - Based on the provided curl example
        ScheduleCreateRequest request = ScheduleCreateRequest.builder()
                .templateType("PRE_GAME")
                .templateName("Match Preview team news and quotes")
                .generateSummary(true)
                .generateStrapline(true)
                .category("2025011511551414715")
                .categoryName("test-propертъ")
                .userId("2018060808173367773")
                .userName("Service Admin")
                .sport("FOOTBALL")
                .scheduleType(ScheduleType.IMMEDIATELY) // "IMMEDIATELY" from curl example
                .matches(List.of(
                    ScheduleMatchRequest.builder()
                        .matchId("c9ad762a-09da-4a40-89b3-f8a5483cb293")
                        .competitionId("92c3e74c-665b-4251-8ddb-d3bb042140f3")
                        .competitionName("Световно клубно първенство")
                        .matchName("Челси - ФК Лос Анджелис")
                        .matchDate(Instant.parse("2025-06-18T19:00:00.000Z"))
                        .build()
                ))
                .build();

        // Act & Assert
        assertDoesNotThrow(() -> validator.validate(request));
        assertEquals(ScheduleType.IMMEDIATELY, request.getScheduleType());
        assertEquals("FOOTBALL", request.getSport());
        assertEquals(1, request.getMatches().size());
        assertEquals("Челси - ФК Лос Анджелис", request.getMatches().get(0).getMatchName());
    }

    private ScheduleCreateRequest createTestRequest(ScheduleType scheduleType) {
        return ScheduleCreateRequest.builder()
                .templateName("Test Template")
                .templateType("PRE_GAME")
                .category("123")
                .categoryName("Test Category")
                .userId("456")
                .userName("Test User")
                .sport("FOOTBALL")
                .generateSummary(true)
                .generateStrapline(true)
                .scheduleType(scheduleType)
                .matches(List.of(
                    ScheduleMatchRequest.builder()
                        .matchId("c9ad762a-09da-4a40-89b3-f8a5483cb293")
                        .competitionId("92c3e74c-665b-4251-8ddb-d3bb042140f3")
                        .competitionName("Test Competition")
                        .matchName("Team A - Team B")
                        .matchDate(Instant.now().plusSeconds(3600))
                        .build()
                ))
                .build();
    }
}
