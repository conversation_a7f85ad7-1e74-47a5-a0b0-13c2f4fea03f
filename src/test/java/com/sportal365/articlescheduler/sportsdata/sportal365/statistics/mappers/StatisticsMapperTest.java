package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.mappers;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.EventDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.ParticipantDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticsApiEventsResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.wrappers.StarResponsesWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.sportal365.articlescheduler.sportsdata.sportal365.statistics.constants.StatisticsConstants.ENTITY_TYPE_PLAYER;
import static org.junit.jupiter.api.Assertions.*;

@DisplayName("StatisticsMapper Tests")
class StatisticsMapperTest {

    private StatisticsMapper statisticsMapper;

    @BeforeEach
    void setUp() {
        statisticsMapper = new StatisticsMapper();
    }

    @Nested
    @DisplayName("map() Method Tests")
    class MapMethodTests {

        @ParameterizedTest
        @NullAndEmptySource
        @DisplayName("Should return empty teams when input is null or empty")
        void shouldReturnEmptyTeamsWhenInputIsNullOrEmpty(List<StarResponsesWrapper> input) {
            MatchDetails.ParticipantStatistics result = statisticsMapper.map(input);

            assertNotNull(result);
            assertNotNull(result.getHomeTeam());
            assertNotNull(result.getAwayTeam());
            assertNull(result.getHomeTeam().getName());
            assertNull(result.getHomeTeam().getStarPlayer());
            assertNull(result.getAwayTeam().getName());
            assertNull(result.getAwayTeam().getStarPlayer());
        }

        @ParameterizedTest
        @ValueSource(ints = {1, 3, 4, 5})
        @DisplayName("Should return empty teams when input size is not exactly 2")
        void shouldReturnEmptyTeamsWhenInputSizeIsNotExactlyTwo(int size) {
            List<StarResponsesWrapper> wrongSizeList = new ArrayList<>();
            for (int i = 0; i < size; i++) {
                wrongSizeList.add(createValidStarResponsesWrapper("Team " + i, "Player " + i));
            }

            MatchDetails.ParticipantStatistics result = statisticsMapper.map(wrongSizeList);

            assertNotNull(result);
            assertNotNull(result.getHomeTeam());
            assertNotNull(result.getAwayTeam());
            assertNull(result.getHomeTeam().getName());
            assertNull(result.getHomeTeam().getStarPlayer());
            assertNull(result.getAwayTeam().getName());
            assertNull(result.getAwayTeam().getStarPlayer());
        }

        @Test
        @DisplayName("Should map valid input with exactly 2 participants")
        void shouldMapValidInputWithExactlyTwoParticipants() {
            List<StarResponsesWrapper> validInput = List.of(
                    createValidStarResponsesWrapper("Home Team", "Home Player"),
                    createValidStarResponsesWrapper("Away Team", "Away Player")
            );

            MatchDetails.ParticipantStatistics result = statisticsMapper.map(validInput);

            assertNotNull(result);
            assertNotNull(result.getHomeTeam());
            assertNotNull(result.getAwayTeam());

            // Verify home team
            assertEquals("Home Team", result.getHomeTeam().getName());
            assertNotNull(result.getHomeTeam().getStarPlayer());
            assertEquals("player1", result.getHomeTeam().getStarPlayer().getId());
            assertEquals("Home Player", result.getHomeTeam().getStarPlayer().getName());

            // Verify away team
            assertEquals("Away Team", result.getAwayTeam().getName());
            assertNotNull(result.getAwayTeam().getStarPlayer());
            assertEquals("player1", result.getAwayTeam().getStarPlayer().getId());
            assertEquals("Away Player", result.getAwayTeam().getStarPlayer().getName());
        }

        @Test
        @DisplayName("Should handle participants with empty statistics")
        void shouldHandleParticipantsWithEmptyStatistics() {
            List<StarResponsesWrapper> inputWithEmptyStats = List.of(
                    createStarResponsesWrapperWithEmptyStats("Home Team", "Home Player"),
                    createStarResponsesWrapperWithEmptyStats("Away Team", "Away Player")
            );

            MatchDetails.ParticipantStatistics result = statisticsMapper.map(inputWithEmptyStats);

            assertNotNull(result);
            assertNotNull(result.getHomeTeam());
            assertNotNull(result.getAwayTeam());

            // Verify that players have empty statistics lists
            assertNotNull(result.getHomeTeam().getStarPlayer().getAverageSeasonStatistics());
            assertTrue(result.getHomeTeam().getStarPlayer().getAverageSeasonStatistics().isEmpty());
            assertNotNull(result.getAwayTeam().getStarPlayer().getAverageSeasonStatistics());
            assertTrue(result.getAwayTeam().getStarPlayer().getAverageSeasonStatistics().isEmpty());
        }

        @Test
        @DisplayName("Should handle participants with null statistics")
        void shouldHandleParticipantsWithNullStatistics() {
            List<StarResponsesWrapper> inputWithNullStats = List.of(
                    createStarResponsesWrapperWithNullStats("Home Team", "Home Player"),
                    createStarResponsesWrapperWithNullStats("Away Team", "Away Player")
            );

            MatchDetails.ParticipantStatistics result = statisticsMapper.map(inputWithNullStats);

            assertNotNull(result);
            assertNotNull(result.getHomeTeam());
            assertNotNull(result.getAwayTeam());

            // Should handle null statistics gracefully
            assertNotNull(result.getHomeTeam().getStarPlayer());
            assertNotNull(result.getAwayTeam().getStarPlayer());
        }

        @Test
        @DisplayName("Should handle empty event responses")
        void shouldHandleEmptyEventResponses() {
            List<StarResponsesWrapper> inputWithEmptyEvents = List.of(
                    createStarResponsesWrapperWithEmptyEvents("Home Team", "Home Player"),
                    createStarResponsesWrapperWithEmptyEvents("Away Team", "Away Player")
            );

            MatchDetails.ParticipantStatistics result = statisticsMapper.map(inputWithEmptyEvents);

            assertNotNull(result);
            assertNotNull(result.getHomeTeam());
            assertNotNull(result.getAwayTeam());

            // Verify that event statistics are empty
            assertNotNull(result.getHomeTeam().getStarPlayer().getLatestEventsStatistics());
            assertTrue(result.getHomeTeam().getStarPlayer().getLatestEventsStatistics().isEmpty());
            assertNotNull(result.getHomeTeam().getStarPlayer().getLatestEventsStatisticsVersusOpponent());
            assertTrue(result.getHomeTeam().getStarPlayer().getLatestEventsStatisticsVersusOpponent().isEmpty());
        }

        @Test
        @DisplayName("Should map complex statistics with multiple events and statistics")
        void shouldMapComplexStatisticsWithMultipleEventsAndStatistics() {
            List<StarResponsesWrapper> complexInput = List.of(
                    createComplexStarResponsesWrapper("Home Team", "Home Player"),
                    createComplexStarResponsesWrapper("Away Team", "Away Player")
            );

            MatchDetails.ParticipantStatistics result = statisticsMapper.map(complexInput);

            assertNotNull(result);

            // Verify home team complex statistics
            MatchDetails.ParticipantStatistics.Player homePlayer = result.getHomeTeam().getStarPlayer();
            assertNotNull(homePlayer.getAverageSeasonStatistics());
            assertEquals(2, homePlayer.getAverageSeasonStatistics().size());

            assertNotNull(homePlayer.getLatestEventsStatistics());
            assertNotNull(homePlayer.getLatestEventsStatisticsVersusOpponent());

            // Verify away team has the same structure
            MatchDetails.ParticipantStatistics.Player awayPlayer = result.getAwayTeam().getStarPlayer();
            assertNotNull(awayPlayer.getAverageSeasonStatistics());
            assertEquals(2, awayPlayer.getAverageSeasonStatistics().size());

            assertNotNull(awayPlayer.getLatestEventsStatistics());
            assertNotNull(awayPlayer.getLatestEventsStatisticsVersusOpponent());
        }
    }

    @Nested
    @DisplayName("Edge Case Tests")
    class EdgeCaseTests {

        @Test
        @DisplayName("Should handle participants with different names in events")
        void shouldHandleParticipantsWithDifferentNamesInEvents() {
            StarResponsesWrapper wrapper = createStarResponsesWrapperWithMismatchedNames();

            List<StarResponsesWrapper> input = List.of(wrapper, createValidStarResponsesWrapper("Away Team", "Away Player"));

            MatchDetails.ParticipantStatistics result = statisticsMapper.map(input);

            assertNotNull(result);
            // Should handle mismatched names gracefully - events won't be found for the player
            assertNotNull(result.getHomeTeam().getStarPlayer().getLatestEventsStatistics());
            assertTrue(result.getHomeTeam().getStarPlayer().getLatestEventsStatistics().isEmpty());
        }

        @ParameterizedTest
        @NullSource
        @DisplayName("Should handle null team names")
        void shouldHandleNullTeamNames(String teamName) {
            List<StarResponsesWrapper> input = List.of(
                    createStarResponsesWrapperWithTeamName(teamName, "Player 1"),
                    createValidStarResponsesWrapper("Away Team", "Player 2")
            );

            MatchDetails.ParticipantStatistics result = statisticsMapper.map(input);

            assertNotNull(result);
            assertEquals(teamName, result.getHomeTeam().getName());
        }
    }

    // Helper methods for creating test data
    private StarResponsesWrapper createValidStarResponsesWrapper(String teamName, String playerName) {
        ParticipantDto participant = createParticipantDto("player1", playerName);
        StatisticsApiEventsResponse latestEvents = createEventsResponse(playerName, "Event 1");
        StatisticsApiEventsResponse versusEvents = createEventsResponse(playerName, "Versus Event 1");

        return new StarResponsesWrapper(teamName, participant, latestEvents, versusEvents);
    }

    private StarResponsesWrapper createStarResponsesWrapperWithEmptyStats(String teamName, String playerName) {
        ParticipantDto participant = createParticipantDtoWithEmptyStats("player1", playerName);
        StatisticsApiEventsResponse latestEvents = createEventsResponse(playerName, "Event 1");
        StatisticsApiEventsResponse versusEvents = createEventsResponse(playerName, "Versus Event 1");

        return new StarResponsesWrapper(teamName, participant, latestEvents, versusEvents);
    }

    private StarResponsesWrapper createStarResponsesWrapperWithNullStats(String teamName, String playerName) {
        ParticipantDto participant = createParticipantDtoWithNullStats("player1", playerName);
        StatisticsApiEventsResponse latestEvents = createEventsResponse(playerName, "Event 1");
        StatisticsApiEventsResponse versusEvents = createEventsResponse(playerName, "Versus Event 1");

        return new StarResponsesWrapper(teamName, participant, latestEvents, versusEvents);
    }

    private StarResponsesWrapper createStarResponsesWrapperWithEmptyEvents(String teamName, String playerName) {
        ParticipantDto participant = createParticipantDto("player1", playerName);
        StatisticsApiEventsResponse emptyEvents = new StatisticsApiEventsResponse();
        emptyEvents.setData(Collections.emptyList());

        return new StarResponsesWrapper(teamName, participant, emptyEvents, emptyEvents);
    }

    private StarResponsesWrapper createComplexStarResponsesWrapper(String teamName, String playerName) {
        ParticipantDto participant = createParticipantDtoWithMultipleStats("player1", playerName);
        StatisticsApiEventsResponse latestEvents = createMultipleEventsResponse(playerName);
        StatisticsApiEventsResponse versusEvents = createEventsResponse(playerName, "Versus Event 1");

        return new StarResponsesWrapper(teamName, participant, latestEvents, versusEvents);
    }

    private StarResponsesWrapper createStarResponsesWrapperWithMismatchedNames() {
        ParticipantDto participant = createParticipantDto("player1", "Player Name");
        StatisticsApiEventsResponse latestEvents = createEventsResponse("Different Player Name", "Event 1");
        StatisticsApiEventsResponse versusEvents = createEventsResponse("Different Player Name", "Versus Event 1");

        return new StarResponsesWrapper("Home Team", participant, latestEvents, versusEvents);
    }

    private StarResponsesWrapper createStarResponsesWrapperWithTeamName(String teamName, String playerName) {
        ParticipantDto participant = createParticipantDto("player1", playerName);
        StatisticsApiEventsResponse latestEvents = createEventsResponse(playerName, "Event 1");
        StatisticsApiEventsResponse versusEvents = createEventsResponse(playerName, "Versus Event 1");

        return new StarResponsesWrapper(teamName, participant, latestEvents, versusEvents);
    }

    private StarResponsesWrapper createStarResponsesWrapperWithNullEventData(String teamName, String playerName) {
        ParticipantDto participant = createParticipantDto("player1", playerName);
        StatisticsApiEventsResponse nullDataEvents = new StatisticsApiEventsResponse();
        nullDataEvents.setData(null);

        return new StarResponsesWrapper(teamName, participant, nullDataEvents, nullDataEvents);
    }

    private ParticipantDto createParticipantDto(String id, String name) {
        ParticipantDto participant = new ParticipantDto();
        participant.setId(id);
        participant.setName(name);
        participant.setEntityType(ENTITY_TYPE_PLAYER);

        List<StatisticDto> statistics = List.of(
                createStatisticDto("36c1b27a-f84a-4351-a9f4-1c23a346076d", "Goals", "5"),
                createStatisticDto("e666f4a4-a496-4432-b815-2a2c9bacca52", "Assists", "3")
        );
        participant.setStatistics(statistics);

        return participant;
    }

    private ParticipantDto createParticipantDtoWithEmptyStats(String id, String name) {
        ParticipantDto participant = new ParticipantDto();
        participant.setId(id);
        participant.setName(name);
        participant.setEntityType(ENTITY_TYPE_PLAYER);
        participant.setStatistics(Collections.emptyList());

        return participant;
    }

    private ParticipantDto createParticipantDtoWithNullStats(String id, String name) {
        ParticipantDto participant = new ParticipantDto();
        participant.setId(id);
        participant.setName(name);
        participant.setEntityType(ENTITY_TYPE_PLAYER);
        participant.setStatistics(null);

        return participant;
    }

    private ParticipantDto createParticipantDtoWithMultipleStats(String id, String name) {
        ParticipantDto participant = new ParticipantDto();
        participant.setId(id);
        participant.setName(name);
        participant.setEntityType(ENTITY_TYPE_PLAYER);

        List<StatisticDto> statistics = List.of(
                createStatisticDto("36c1b27a-f84a-4351-a9f4-1c23a346076d", "Goals", "8"),
                createStatisticDto("e666f4a4-a496-4432-b815-2a2c9bacca52", "Assists", "6")
        );
        participant.setStatistics(statistics);

        return participant;
    }

    private StatisticsApiEventsResponse createEventsResponse(String playerName, String eventName) {
        ParticipantDto eventParticipant = new ParticipantDto();
        eventParticipant.setId("player1");
        eventParticipant.setName(playerName);
        eventParticipant.setEntityType(ENTITY_TYPE_PLAYER);
        eventParticipant.setStatistics(List.of(
                createStatisticDto("36c1b27a-f84a-4351-a9f4-1c23a346076d", "Event Goals", "2"),
                createStatisticDto("e666f4a4-a496-4432-b815-2a2c9bacca52", "Event Assists", "1")
        ));

        EventDto event = new EventDto();
        event.setName(eventName);
        event.setParticipants(List.of(eventParticipant));

        StatisticsApiEventsResponse response = new StatisticsApiEventsResponse();
        response.setData(List.of(event));

        return response;
    }

    private StatisticsApiEventsResponse createMultipleEventsResponse(String playerName) {
        ParticipantDto eventParticipant1 = new ParticipantDto();
        eventParticipant1.setId("player1");
        eventParticipant1.setName(playerName);
        eventParticipant1.setEntityType(ENTITY_TYPE_PLAYER);
        eventParticipant1.setStatistics(List.of(
                createStatisticDto("36c1b27a-f84a-4351-a9f4-1c23a346076d", "Event Goals", "1"),
                createStatisticDto("e666f4a4-a496-4432-b815-2a2c9bacca52", "Event Assists", "2")
        ));

        ParticipantDto eventParticipant2 = new ParticipantDto();
        eventParticipant2.setId("player1");
        eventParticipant2.setName(playerName);
        eventParticipant2.setEntityType(ENTITY_TYPE_PLAYER);
        eventParticipant2.setStatistics(List.of(
                createStatisticDto("36c1b27a-f84a-4351-a9f4-1c23a346076d", "Event Goals", "3"),
                createStatisticDto("e666f4a4-a496-4432-b815-2a2c9bacca52", "Event Assists", "0")
        ));

        EventDto event1 = new EventDto();
        event1.setName("Event 1");
        event1.setParticipants(List.of(eventParticipant1));

        EventDto event2 = new EventDto();
        event2.setName("Event 2");
        event2.setParticipants(List.of(eventParticipant2));

        StatisticsApiEventsResponse response = new StatisticsApiEventsResponse();
        response.setData(List.of(event1, event2));

        return response;
    }

    private StatisticDto createStatisticDto(String id, String name, String value) {
        StatisticDto statistic = new StatisticDto();
        statistic.setId(id);
        statistic.setName(name);
        statistic.setValue(value);
        return statistic;
    }
}
