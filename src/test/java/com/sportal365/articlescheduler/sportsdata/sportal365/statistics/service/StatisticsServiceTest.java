package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.service;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.StatisticsApiClient;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.ParticipantDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticsApiAggregateResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticsApiEventsResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.wrappers.StarResponsesWrapper;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.mappers.StatisticsMapper;
import com.sportal365.common.enums.SportEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static com.sportal365.articlescheduler.sportsdata.sportal365.statistics.constants.StatisticsConstants.ENTITY_TYPE_PLAYER;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("StatisticsService Tests")
class StatisticsServiceTest {

    @Mock
    private StatisticsApiClient statisticsApiClient;

    @Mock
    private StatisticsMapper statisticsMapper;

    @InjectMocks
    private StatisticsService statisticsService;

    private MatchDetails baseMatchDetails;
    private Schedule baseSchedule;

    @BeforeEach
    void setUp() {
        baseMatchDetails = createBaseMatchDetails();
        baseSchedule = createBaseSchedule();

        // Reset mocks before each test to avoid unnecessary stubbing exceptions
        reset(statisticsApiClient, statisticsMapper);
    }

    @Nested
    @DisplayName("enrich() Method Tests")
    class EnrichMethodTests {

        @Test
        @DisplayName("Should return enriched match details when API calls succeed")
        void shouldReturnEnrichedMatchDetailsWhenApiCallsSucceed() {
            // Arrange
            StatisticsApiEventsResponse validEventsResponse = createValidEventsResponse();
            MatchDetails.ParticipantStatistics expectedStatistics = createMockParticipantStatistics();

            // Mock responses for both home and away teams
            when(statisticsApiClient.getParticipantSeasonStatistics(eq("test-project"), eq("home-team-id"), eq("season-123")))
                    .thenReturn(Mono.just(createValidStatisticsResponseForHomeTeam()));
            when(statisticsApiClient.getParticipantSeasonStatistics(eq("test-project"), eq("away-team-id"), eq("season-123")))
                    .thenReturn(Mono.just(createValidStatisticsResponseForAwayTeam()));
            when(statisticsApiClient.getParticipantSeasonStatisticsDiscrete(anyString(), anyString(), anyString()))
                    .thenReturn(Mono.just(validEventsResponse));
            when(statisticsMapper.map(any())).thenReturn(expectedStatistics);

            // Act
            MatchDetails result = statisticsService.enrich(baseMatchDetails, baseSchedule);

            // Assert
            assertNotNull(result);
            assertEquals(expectedStatistics, result.getStatistics());
            assertEquals(baseMatchDetails.getMatchId(), result.getMatchId());
            assertEquals(baseMatchDetails.getHomeTeam(), result.getHomeTeam());
            assertEquals(baseMatchDetails.getAwayTeam(), result.getAwayTeam());

            // Verify interactions
            verify(statisticsApiClient, times(2)).getParticipantSeasonStatistics(
                    eq("test-project"), anyString(), eq("season-123"));
            verify(statisticsApiClient, times(2)).getParticipantSeasonStatisticsDiscrete(
                    eq("test-project"), anyString(), eq("season-123"));
            verify(statisticsMapper, times(1)).map(any());
        }

        @Test
        @DisplayName("Should return match details with empty statistics when API returns empty Mono")
        void shouldReturnMatchDetailsWithEmptyStatisticsWhenApiReturnsEmptyMono() {
            // Arrange
            when(statisticsApiClient.getParticipantSeasonStatistics(anyString(), anyString(), anyString()))
                    .thenReturn(Mono.empty()); // API returns empty Mono (no value emitted)

            MatchDetails.ParticipantStatistics emptyStatistics = createEmptyParticipantStatistics();
            when(statisticsMapper.map(Collections.emptyList())).thenReturn(emptyStatistics);

            // Act
            MatchDetails result = statisticsService.enrich(baseMatchDetails, baseSchedule);

            // Assert
            assertNotNull(result);
            assertEquals(emptyStatistics, result.getStatistics());

            // Verify that mapper was called with empty list
            verify(statisticsMapper).map(Collections.emptyList());
        }

        @Test
        @DisplayName("Should return match details with empty statistics when no highest rated player found")
        void shouldReturnMatchDetailsWithEmptyStatisticsWhenNoHighestRatedPlayerFound() {
            // Arrange
            StatisticsApiAggregateResponse emptyResponse = new StatisticsApiAggregateResponse();
            emptyResponse.setData(Collections.emptyList());
            
            when(statisticsApiClient.getParticipantSeasonStatistics(anyString(), anyString(), anyString()))
                    .thenReturn(Mono.just(emptyResponse));

            MatchDetails.ParticipantStatistics emptyStatistics = createEmptyParticipantStatistics();
            when(statisticsMapper.map(Collections.emptyList())).thenReturn(emptyStatistics);

            // Act
            MatchDetails result = statisticsService.enrich(baseMatchDetails, baseSchedule);

            // Assert
            assertNotNull(result);
            assertEquals(emptyStatistics, result.getStatistics());

            // Verify that mapper was called with empty list
            verify(statisticsMapper).map(Collections.emptyList());
        }

        @Test
        @DisplayName("Should handle empty participant IDs gracefully")
        void shouldHandleEmptyParticipantIdsGracefully() {
            // Arrange
            MatchDetails matchDetailsWithEmptyParticipants = MatchDetails.builder()
                    .homeTeamId("")
                    .awayTeamId("")
                    .homeTeam("Home Team")
                    .awayTeam("Away Team")
                    .seasonId("season-123")
                    .lastMeetings(Collections.emptyList())
                    .lineup(createMockLineup())
                    .build();

            // Mock API to return empty Mono for empty participant IDs
            when(statisticsApiClient.getParticipantSeasonStatistics(anyString(), eq(""), anyString()))
                    .thenReturn(Mono.empty());

            MatchDetails.ParticipantStatistics emptyStatistics = createEmptyParticipantStatistics();
            when(statisticsMapper.map(Collections.emptyList())).thenReturn(emptyStatistics);

            // Act
            MatchDetails result = statisticsService.enrich(matchDetailsWithEmptyParticipants, baseSchedule);

            // Assert
            assertNotNull(result);
            assertEquals(emptyStatistics, result.getStatistics());
        }

        @Test
        @DisplayName("Should return empty statistics when participant IDs are null")
        void shouldReturnEmptyStatisticsWhenParticipantIdsAreNull() {
            // Arrange
            MatchDetails matchDetailsWithNullParticipantIds = MatchDetails.builder()
                    .homeTeamId("home-team-id")
                    .awayTeamId("away-team-id")
                    .homeTeam("Home Team")
                    .awayTeam("Away Team")
                    .seasonId("season-123")
                    .lastMeetings(Collections.emptyList())
                    .lineup(createMockLineup())
                    .build();

            // Override the getParticipantIds() method to return null using a spy
            MatchDetails spyMatchDetails = spy(matchDetailsWithNullParticipantIds);
            when(spyMatchDetails.getParticipantIds()).thenReturn(null);

            MatchDetails.ParticipantStatistics emptyStatistics = createEmptyParticipantStatistics();
            when(statisticsMapper.map(Collections.emptyList())).thenReturn(emptyStatistics);

            // Act
            MatchDetails result = statisticsService.enrich(spyMatchDetails, baseSchedule);

            // Assert
            assertNotNull(result);
            assertEquals(emptyStatistics, result.getStatistics());

            // Verify that no API calls were made since we returned early
            verify(statisticsApiClient, never()).getParticipantSeasonStatistics(anyString(), anyString(), anyString());
            verify(statisticsMapper).map(Collections.emptyList());
        }



        @ParameterizedTest
        @NullAndEmptySource
        @DisplayName("Should handle match details with null or empty last meetings")
        void shouldHandleMatchDetailsWithNullOrEmptyLastMeetings(List<MatchDetails.LastMeeting> lastMeetings) {
            // Arrange
            MatchDetails matchDetailsWithLastMeetings = baseMatchDetails.toBuilder()
                    .lastMeetings(lastMeetings)
                    .build();

            setupSuccessfulApiMocks();
            MatchDetails.ParticipantStatistics expectedStatistics = createMockParticipantStatistics();
            when(statisticsMapper.map(any())).thenReturn(expectedStatistics);

            // Act
            MatchDetails result = statisticsService.enrich(matchDetailsWithLastMeetings, baseSchedule);

            // Assert
            assertNotNull(result);
            assertEquals(expectedStatistics, result.getStatistics());

            // Verify that discrete statistics was not called for versus events
            verify(statisticsApiClient, never()).getParticipantStatisticsDiscrete(anyString(), anyString(), anyList());
        }
    }

    @Nested
    @DisplayName("API Client Interaction Tests")
    class ApiClientInteractionTests {

        @Test
        @DisplayName("Should call API client with correct parameters")
        void shouldCallApiClientWithCorrectParameters() {
            // Arrange
            setupSuccessfulApiMocks();
            when(statisticsMapper.map(any())).thenReturn(createMockParticipantStatistics());

            // Act
            statisticsService.enrich(baseMatchDetails, baseSchedule);

            // Assert
            verify(statisticsApiClient).getParticipantSeasonStatistics(
                    "test-project", "home-team-id", "season-123");
            verify(statisticsApiClient).getParticipantSeasonStatistics(
                    "test-project", "away-team-id", "season-123");
        }

        @Test
        @DisplayName("Should handle API client exceptions gracefully")
        void shouldHandleApiClientExceptionsGracefully() {
            // Arrange
            when(statisticsApiClient.getParticipantSeasonStatistics(anyString(), anyString(), anyString()))
                    .thenThrow(new RuntimeException("API Error"));

            // Act & Assert
            assertThrows(RuntimeException.class, () -> statisticsService.enrich(baseMatchDetails, baseSchedule));
        }

        @Test
        @DisplayName("Should handle reactive stream errors")
        void shouldHandleReactiveStreamErrors() {
            // Arrange
            when(statisticsApiClient.getParticipantSeasonStatistics(anyString(), anyString(), anyString()))
                    .thenReturn(Mono.error(new RuntimeException("Reactive error")));

            // Act & Assert
            assertThrows(RuntimeException.class, () -> statisticsService.enrich(baseMatchDetails, baseSchedule));
        }
    }

    @Nested
    @DisplayName("Edge Case Tests")
    class EdgeCaseTests {

        @Test
        @DisplayName("Should handle match details with null season ID")
        void shouldHandleMatchDetailsWithNullSeasonId() {
            // Arrange
            MatchDetails matchDetailsWithNullSeason = baseMatchDetails.toBuilder()
                    .seasonId(null)
                    .build();

            // Act & Assert - Expect specific exception when API client receives null seasonId
            assertThrows(NullPointerException.class, () ->
                statisticsService.enrich(matchDetailsWithNullSeason, baseSchedule),
                "Should throw NullPointerException when seasonId is null");
        }

        @ParameterizedTest
        @MethodSource("provideInvalidMatchDetailsScenarios")
        @DisplayName("Should handle various invalid match details scenarios")
        void shouldHandleInvalidMatchDetailsScenarios(MatchDetails invalidMatchDetails) {
            // Act & Assert - Expect specific exception for invalid match details
            assertThrows(NullPointerException.class, () ->
                statisticsService.enrich(invalidMatchDetails, baseSchedule),
                "Should throw NullPointerException when required match details fields are null or invalid");
        }

        static Stream<MatchDetails> provideInvalidMatchDetailsScenarios() {
            return Stream.of(
                    MatchDetails.builder().homeTeamId(null).awayTeamId("away").seasonId("season").build(),
                    MatchDetails.builder().homeTeamId("home").awayTeamId(null).seasonId("season").build(),
                    MatchDetails.builder().homeTeamId("").awayTeamId("away").seasonId("season").build()
            );
        }

        @Test
        @DisplayName("Should handle complex scenario with last meetings and successful API calls")
        void shouldHandleComplexScenarioWithLastMeetingsAndSuccessfulApiCalls() {
            // Arrange
            MatchDetails complexMatchDetails = createComplexMatchDetails();
            StatisticsApiEventsResponse validEventsResponse = createValidEventsResponse();
            MatchDetails.ParticipantStatistics expectedStatistics = createMockParticipantStatistics();

            // Mock responses for both teams
            when(statisticsApiClient.getParticipantSeasonStatistics(eq("test-project"), eq("home-team-id"), eq("season-123")))
                    .thenReturn(Mono.just(createValidStatisticsResponseForHomeTeam()));
            when(statisticsApiClient.getParticipantSeasonStatistics(eq("test-project"), eq("away-team-id"), eq("season-123")))
                    .thenReturn(Mono.just(createValidStatisticsResponseForAwayTeam()));
            when(statisticsApiClient.getParticipantSeasonStatisticsDiscrete(anyString(), anyString(), anyString()))
                    .thenReturn(Mono.just(validEventsResponse));
            when(statisticsApiClient.getParticipantStatisticsDiscrete(anyString(), anyString(), anyList()))
                    .thenReturn(Mono.just(validEventsResponse));
            when(statisticsMapper.map(any())).thenReturn(expectedStatistics);

            // Act
            MatchDetails result = statisticsService.enrich(complexMatchDetails, baseSchedule);

            // Assert
            assertNotNull(result);
            assertEquals(expectedStatistics, result.getStatistics());

            // Verify all API calls were made
            verify(statisticsApiClient, times(2)).getParticipantSeasonStatistics(anyString(), anyString(), anyString());
            verify(statisticsApiClient, times(2)).getParticipantSeasonStatisticsDiscrete(anyString(), anyString(), anyString());
            verify(statisticsApiClient, times(2)).getParticipantStatisticsDiscrete(anyString(), anyString(), anyList());
        }

        @Test
        @DisplayName("Should handle partial API failures gracefully")
        void shouldHandlePartialApiFailuresGracefully() {
            // Arrange - First call returns empty Mono (null after block()), so service returns early
            when(statisticsApiClient.getParticipantSeasonStatistics("test-project", "home-team-id", "season-123"))
                    .thenReturn(Mono.empty());

            MatchDetails.ParticipantStatistics emptyStatistics = createEmptyParticipantStatistics();
            when(statisticsMapper.map(Collections.emptyList())).thenReturn(emptyStatistics);

            // Act
            MatchDetails result = statisticsService.enrich(baseMatchDetails, baseSchedule);

            // Assert
            assertNotNull(result);
            assertEquals(emptyStatistics, result.getStatistics());

            // Verify that processing stopped after first null response
            verify(statisticsMapper).map(Collections.emptyList());
            // Verify that only the first API call was made
            verify(statisticsApiClient, times(1)).getParticipantSeasonStatistics("test-project", "home-team-id", "season-123");
            verify(statisticsApiClient, never()).getParticipantSeasonStatistics("test-project", "away-team-id", "season-123");
        }
    }

    @Nested
    @DisplayName("Mapper Integration Tests")
    class MapperIntegrationTests {

        @Test
        @DisplayName("Should pass correct data to mapper")
        void shouldPassCorrectDataToMapper() {
            // Arrange
            StatisticsApiEventsResponse validEventsResponse = createValidEventsResponse();
            MatchDetails.ParticipantStatistics expectedStatistics = createMockParticipantStatistics();

            // Mock responses for both teams
            when(statisticsApiClient.getParticipantSeasonStatistics(eq("test-project"), eq("home-team-id"), eq("season-123")))
                    .thenReturn(Mono.just(createValidStatisticsResponseForHomeTeam()));
            when(statisticsApiClient.getParticipantSeasonStatistics(eq("test-project"), eq("away-team-id"), eq("season-123")))
                    .thenReturn(Mono.just(createValidStatisticsResponseForAwayTeam()));
            when(statisticsApiClient.getParticipantSeasonStatisticsDiscrete(anyString(), anyString(), anyString()))
                    .thenReturn(Mono.just(validEventsResponse));
            when(statisticsMapper.map(any())).thenReturn(expectedStatistics);

            // Act
            statisticsService.enrich(baseMatchDetails, baseSchedule);

            // Assert
            verify(statisticsMapper).map(argThat(wrappers -> {
                assertNotNull(wrappers);
                assertEquals(2, wrappers.size());

                // Verify first wrapper (home team)
                StarResponsesWrapper homeWrapper = wrappers.get(0);
                assertEquals("Home Team", homeWrapper.getTeamName());
                assertNotNull(homeWrapper.getParticipantWithAggregateStatistics());
                assertNotNull(homeWrapper.getParticipantLatestEventsResponse());
                assertNotNull(homeWrapper.getParticipantLatestVersusEventResponse());

                // Verify second wrapper (away team)
                StarResponsesWrapper awayWrapper = wrappers.get(1);
                assertEquals("Away Team", awayWrapper.getTeamName());
                assertNotNull(awayWrapper.getParticipantWithAggregateStatistics());
                assertNotNull(awayWrapper.getParticipantLatestEventsResponse());
                assertNotNull(awayWrapper.getParticipantLatestVersusEventResponse());

                return true;
            }));
        }

        @Test
        @DisplayName("Should handle mapper returning null")
        void shouldHandleMapperReturningNull() {
            // Arrange
            setupSuccessfulApiMocks();
            when(statisticsMapper.map(any())).thenReturn(null);

            // Act
            MatchDetails result = statisticsService.enrich(baseMatchDetails, baseSchedule);

            // Assert
            assertNotNull(result);
            assertNull(result.getStatistics());
        }

        @Test
        @DisplayName("Should handle mapper throwing exception")
        void shouldHandleMapperThrowingException() {
            // Arrange
            setupSuccessfulApiMocks();
            when(statisticsMapper.map(any())).thenThrow(new RuntimeException("Mapper error"));

            // Act & Assert
            assertThrows(RuntimeException.class, () -> statisticsService.enrich(baseMatchDetails, baseSchedule));
        }
    }

    // Helper methods for creating test data
    private MatchDetails createBaseMatchDetails() {
        return MatchDetails.builder()
                .matchId("match-123")
                .homeTeamId("home-team-id")
                .awayTeamId("away-team-id")
                .homeTeam("Home Team")
                .awayTeam("Away Team")
                .seasonId("season-123")

                .lastMeetings(Collections.emptyList())
                .lineup(createMockLineup())
                .build();
    }

    private Schedule createBaseSchedule() {
        return Schedule.builder()
                .projectDomain("test-project")
                .sport(SportEnum.FOOTBALL)
                .generationTime(Instant.now())
                .build();
    }

    private MatchDetails createComplexMatchDetails() {
        List<MatchDetails.LastMeeting> lastMeetings = List.of(
                MatchDetails.LastMeeting.builder().id("meeting1").build(),
                MatchDetails.LastMeeting.builder().id("meeting2").build()
        );

        return baseMatchDetails.toBuilder()
                .lastMeetings(lastMeetings)
                .build();
    }

    private MatchDetails.Lineup createMockLineup() {
        MatchDetails.Lineup.TeamDetails.Player homePlayer = MatchDetails.Lineup.TeamDetails.Player.builder()
                .player(MatchDetails.Lineup.TeamDetails.Player.PlayerDetails.builder()
                        .name("Home Player")
                        .position("MIDFIELDER")
                        .active(true)
                        .build())
                .type(MatchDetails.Lineup.TeamDetails.Player.Type.builder()
                        .name("Starting")
                        .build())
                .build();

        MatchDetails.Lineup.TeamDetails.Player awayPlayer = MatchDetails.Lineup.TeamDetails.Player.builder()
                .player(MatchDetails.Lineup.TeamDetails.Player.PlayerDetails.builder()
                        .name("Away Player")
                        .position("FORWARD")
                        .active(true)
                        .build())
                .type(MatchDetails.Lineup.TeamDetails.Player.Type.builder()
                        .name("Starting")
                        .build())
                .build();

        MatchDetails.Lineup.TeamDetails homeTeamDetails = MatchDetails.Lineup.TeamDetails.builder()
                .players(List.of(homePlayer))
                .build();

        MatchDetails.Lineup.TeamDetails awayTeamDetails = MatchDetails.Lineup.TeamDetails.builder()
                .players(List.of(awayPlayer))
                .build();

        return MatchDetails.Lineup.builder()
                .homeTeam(homeTeamDetails)
                .awayTeam(awayTeamDetails)
                .build();
    }

    private void setupSuccessfulApiMocks() {
        StatisticsApiAggregateResponse validResponse = createValidStatisticsResponse();
        StatisticsApiEventsResponse validEventsResponse = createValidEventsResponse();

        when(statisticsApiClient.getParticipantSeasonStatistics(anyString(), anyString(), anyString()))
                .thenReturn(Mono.just(validResponse));
        when(statisticsApiClient.getParticipantSeasonStatisticsDiscrete(anyString(), anyString(), anyString()))
                .thenReturn(Mono.just(validEventsResponse));
    }

    private StatisticsApiAggregateResponse createValidStatisticsResponse() {
        // Create statistics that will pass the findHighestRatedPlayer criteria
        StatisticDto ratingStatistic = new StatisticDto();
        ratingStatistic.setId("6c324825-0b0c-4788-8812-df51b65d0b51"); // STATISTIC_RATING_AVG_ID
        ratingStatistic.setName("Rating");
        ratingStatistic.setValue("7.5");

        StatisticDto startsStatistic = new StatisticDto();
        startsStatistic.setId("a3f81922-13dd-4988-bc91-812a68681482"); // STATISTIC_STARTED_ID
        startsStatistic.setName("Events Started");
        startsStatistic.setValue("5"); // Above minimum of 3

        ParticipantDto participant = new ParticipantDto();
        participant.setId("player1");
        participant.setName("Home Player"); // Must match exactly with lineup player name
        participant.setEntityType(ENTITY_TYPE_PLAYER);
        participant.setStatistics(List.of(ratingStatistic, startsStatistic));

        StatisticsApiAggregateResponse response = new StatisticsApiAggregateResponse();
        response.setData(List.of(participant));
        return response;
    }

    private StatisticsApiAggregateResponse createValidStatisticsResponseForHomeTeam() {
        StatisticDto ratingStatistic = new StatisticDto();
        ratingStatistic.setId("6c324825-0b0c-4788-8812-df51b65d0b51"); // STATISTIC_RATING_AVG_ID
        ratingStatistic.setName("Rating");
        ratingStatistic.setValue("7.5");

        StatisticDto startsStatistic = new StatisticDto();
        startsStatistic.setId("a3f81922-13dd-4988-bc91-812a68681482"); // STATISTIC_STARTED_ID
        startsStatistic.setName("Events Started");
        startsStatistic.setValue("5");

        ParticipantDto participant = new ParticipantDto();
        participant.setId("home-player-1");
        participant.setName("Home Player"); // Must match lineup player name exactly
        participant.setEntityType(ENTITY_TYPE_PLAYER);
        participant.setStatistics(List.of(ratingStatistic, startsStatistic));

        StatisticsApiAggregateResponse response = new StatisticsApiAggregateResponse();
        response.setData(List.of(participant));
        return response;
    }

    private StatisticsApiAggregateResponse createValidStatisticsResponseForAwayTeam() {
        StatisticDto ratingStatistic = new StatisticDto();
        ratingStatistic.setId("6c324825-0b0c-4788-8812-df51b65d0b51"); // STATISTIC_RATING_AVG_ID
        ratingStatistic.setName("Rating");
        ratingStatistic.setValue("8.0");

        StatisticDto startsStatistic = new StatisticDto();
        startsStatistic.setId("a3f81922-13dd-4988-bc91-812a68681482"); // STATISTIC_STARTED_ID
        startsStatistic.setName("Events Started");
        startsStatistic.setValue("6");

        ParticipantDto participant = new ParticipantDto();
        participant.setId("away-player-1");
        participant.setName("Away Player"); // Must match lineup player name exactly
        participant.setEntityType(ENTITY_TYPE_PLAYER);
        participant.setStatistics(List.of(ratingStatistic, startsStatistic));

        StatisticsApiAggregateResponse response = new StatisticsApiAggregateResponse();
        response.setData(List.of(participant));
        return response;
    }

    private StatisticsApiEventsResponse createValidEventsResponse() {
        StatisticsApiEventsResponse response = new StatisticsApiEventsResponse();
        response.setData(Collections.emptyList()); // Empty events for simplicity
        return response;
    }

    private MatchDetails.ParticipantStatistics createMockParticipantStatistics() {
        MatchDetails.ParticipantStatistics.Player homePlayer = MatchDetails.ParticipantStatistics.Player.builder()
                .id("player1")
                .name("Home Star Player")
                .averageSeasonStatistics(Collections.emptyList())
                .latestEventsStatistics(Collections.emptyList())
                .latestEventsStatisticsVersusOpponent(Collections.emptyList())
                .build();

        MatchDetails.ParticipantStatistics.Player awayPlayer = MatchDetails.ParticipantStatistics.Player.builder()
                .id("player2")
                .name("Away Star Player")
                .averageSeasonStatistics(Collections.emptyList())
                .latestEventsStatistics(Collections.emptyList())
                .latestEventsStatisticsVersusOpponent(Collections.emptyList())
                .build();

        MatchDetails.ParticipantStatistics.Team homeTeam = MatchDetails.ParticipantStatistics.Team.builder()
                .name("Home Team")
                .starPlayer(homePlayer)
                .build();

        MatchDetails.ParticipantStatistics.Team awayTeam = MatchDetails.ParticipantStatistics.Team.builder()
                .name("Away Team")
                .starPlayer(awayPlayer)
                .build();

        return MatchDetails.ParticipantStatistics.builder()
                .homeTeam(homeTeam)
                .awayTeam(awayTeam)
                .build();
    }

    private MatchDetails.ParticipantStatistics createEmptyParticipantStatistics() {
        MatchDetails.ParticipantStatistics.Team emptyTeam = MatchDetails.ParticipantStatistics.Team.builder().build();

        return MatchDetails.ParticipantStatistics.builder()
                .homeTeam(emptyTeam)
                .awayTeam(emptyTeam)
                .build();
    }
}
