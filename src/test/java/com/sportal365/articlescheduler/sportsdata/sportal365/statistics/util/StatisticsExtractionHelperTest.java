package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.util;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.ParticipantDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticsApiAggregateResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.wrappers.TeamInfoWrapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.sportal365.articlescheduler.sportsdata.sportal365.statistics.constants.StatisticsConstants.*;
import static org.junit.jupiter.api.Assertions.*;

@DisplayName("StatisticsExtractionHelper Tests")
class StatisticsExtractionHelperTest {

    @Nested
    @DisplayName("extractLastMeetingIds() Tests")
    class ExtractLastMeetingIdsTests {

        @ParameterizedTest
        @NullAndEmptySource
        @DisplayName("Should return empty list when lastMeetings is null or empty")
        void shouldReturnEmptyListWhenLastMeetingsIsNullOrEmpty(List<MatchDetails.LastMeeting> lastMeetings) {
            MatchDetails matchDetails = MatchDetails.builder()
                    .lastMeetings(lastMeetings)
                    .build();

            List<String> result = StatisticsExtractionHelper.extractLastMeetingIds(matchDetails);

            assertNotNull(result);
            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Should extract meeting IDs when lastMeetings contains valid data")
        void shouldExtractMeetingIdsWhenLastMeetingsContainsValidData() {
            List<MatchDetails.LastMeeting> lastMeetings = List.of(
                    MatchDetails.LastMeeting.builder().id("meeting1").build(),
                    MatchDetails.LastMeeting.builder().id("meeting2").build(),
                    MatchDetails.LastMeeting.builder().id("meeting3").build()
            );
            
            MatchDetails matchDetails = MatchDetails.builder()
                    .lastMeetings(lastMeetings)
                    .build();
            
            List<String> result = StatisticsExtractionHelper.extractLastMeetingIds(matchDetails);
            
            assertNotNull(result);
            assertEquals(3, result.size());
            assertEquals(List.of("meeting1", "meeting2", "meeting3"), result);
        }

        @Test
        @DisplayName("Should handle lastMeetings with null IDs")
        void shouldHandleLastMeetingsWithNullIds() {
            List<MatchDetails.LastMeeting> lastMeetings = new ArrayList<>();
            lastMeetings.add(MatchDetails.LastMeeting.builder().id("meeting1").build());
            lastMeetings.add(MatchDetails.LastMeeting.builder().id(null).build());
            lastMeetings.add(MatchDetails.LastMeeting.builder().id("meeting3").build());

            MatchDetails matchDetails = MatchDetails.builder()
                    .lastMeetings(lastMeetings)
                    .build();

            List<String> result = StatisticsExtractionHelper.extractLastMeetingIds(matchDetails);

            assertNotNull(result);
            assertEquals(3, result.size());
            // Check individual elements since List.of doesn't allow null values
            assertEquals("meeting1", result.get(0));
            assertNull(result.get(1));
            assertEquals("meeting3", result.get(2));
        }
    }

    @Nested
    @DisplayName("determineTeamInfo() Tests")
    class DetermineTeamInfoTests {

        @Test
        @DisplayName("Should return home team info when participant ID matches home team")
        void shouldReturnHomeTeamInfoWhenParticipantIdMatchesHomeTeam() {
            MatchDetails matchDetails = createMatchDetailsWithLineup();
            
            TeamInfoWrapper result = StatisticsExtractionHelper.determineTeamInfo("home-team-id", matchDetails);
            
            assertNotNull(result);
            assertEquals("Home Team", result.name());
            assertNotNull(result.players());
            assertEquals(2, result.players().size());
        }
        @Test
        @DisplayName("Should return away team info when participant ID matches away team")
        void shouldReturnAwayTeamInfoWhenParticipantIdMatchesAwayTeam() {
            MatchDetails matchDetails = createMatchDetailsWithLineup();
            
            TeamInfoWrapper result = StatisticsExtractionHelper.determineTeamInfo("away-team-id", matchDetails);
            
            assertNotNull(result);
            assertEquals("Away Team", result.name());
            assertNotNull(result.players());
            assertEquals(2, result.players().size());
        }

        @Test
        @DisplayName("Should fallback to squad when lineup is not available")
        void shouldFallbackToSquadWhenLineupIsNotAvailable() {
            MatchDetails matchDetails = createMatchDetailsWithSquadOnly();
            
            TeamInfoWrapper result = StatisticsExtractionHelper.determineTeamInfo("home-team-id", matchDetails);
            
            assertNotNull(result);
            assertEquals("Home Team", result.name());
            assertNotNull(result.players());
            assertEquals(2, result.players().size());
        }

        @Test
        @DisplayName("Should handle null lineup and squad gracefully")
        void shouldHandleNullLineupAndSquadGracefully() {
            MatchDetails matchDetails = MatchDetails.builder()
                    .homeTeamId("home-team-id")
                    .awayTeamId("away-team-id")
                    .homeTeam("Home Team")
                    .awayTeam("Away Team")
                    .lineup(null)
                    .squad(null)
                    .build();

            // Should handle gracefully by returning a TeamInfoWrapper with empty players list
            TeamInfoWrapper result = StatisticsExtractionHelper.determineTeamInfo("home-team-id", matchDetails);

            assertNotNull(result);
            assertEquals("Home Team", result.name());
            assertNotNull(result.players());
            assertTrue(result.players().isEmpty(), "Should return empty players list when both lineup and squad are null");
        }
    }

    @Nested
    @DisplayName("findHighestRatedPlayer() Tests")
    class FindHighestRatedPlayerTests {

        @Test
        @DisplayName("Should return null when statistics data is empty")
        void shouldReturnNullWhenStatisticsDataIsEmpty() {
            StatisticsApiAggregateResponse statistics = new StatisticsApiAggregateResponse();
            statistics.setData(Collections.emptyList());
            List<StatisticsPlayer> players = createValidStatisticsPlayers();
            
            ParticipantDto result = StatisticsExtractionHelper.findHighestRatedPlayer(statistics, players);
            
            assertNull(result);
        }

        @Test
        @DisplayName("Should return null when statisticsPlayers is empty")
        void shouldReturnNullWhenStatisticsPlayersIsEmpty() {
            StatisticsApiAggregateResponse statistics = createValidStatisticsResponse();
            
            ParticipantDto result = StatisticsExtractionHelper.findHighestRatedPlayer(statistics, Collections.emptyList());
            
            assertNull(result);
        }

        @Test
        @DisplayName("Should return highest rated active player with minimum starts")
        void shouldReturnHighestRatedActivePlayerWithMinimumStarts() {
            StatisticsApiAggregateResponse statistics = createStatisticsResponseWithMultiplePlayers();
            List<StatisticsPlayer> players = createValidStatisticsPlayers();

            ParticipantDto result = StatisticsExtractionHelper.findHighestRatedPlayer(statistics, players);

            assertNotNull(result);
            assertEquals("player2", result.getId());
            assertEquals("Player Two", result.getName());

            // Verify that the helper method uses the correct statistic ID constants
            assertNotNull(result.getStatistics());
            assertEquals(2, result.getStatistics().size());

            // Verify rating statistic uses the correct constant ID
            StatisticDto ratingStatistic = result.getStatistics().stream()
                    .filter(stat -> STATISTIC_RATING_AVG_ID.equals(stat.getId()))
                    .findFirst()
                    .orElse(null);
            assertNotNull(ratingStatistic, "Rating statistic should use STATISTIC_RATING_AVG_ID constant");
            assertEquals("Rating", ratingStatistic.getName());
            assertEquals("8.5", ratingStatistic.getValue());

            // Verify starts statistic uses the correct constant ID
            StatisticDto startsStatistic = result.getStatistics().stream()
                    .filter(stat -> STATISTIC_STARTED_ID.equals(stat.getId()))
                    .findFirst()
                    .orElse(null);
            assertNotNull(startsStatistic, "Starts statistic should use STATISTIC_STARTED_ID constant");
            assertEquals("Events Started", startsStatistic.getName());
            assertEquals("7", startsStatistic.getValue());
        }

        @Test
        @DisplayName("Should return null when no players meet minimum starts requirement")
        void shouldReturnNullWhenNoPlayersMeetMinimumStartsRequirement() {
            StatisticsApiAggregateResponse statistics = createStatisticsResponseWithLowStarts();
            List<StatisticsPlayer> players = createValidStatisticsPlayers();
            
            ParticipantDto result = StatisticsExtractionHelper.findHighestRatedPlayer(statistics, players);
            
            assertNull(result);
        }

        @Test
        @DisplayName("Should return null when no players are active")
        void shouldReturnNullWhenNoPlayersAreActive() {
            StatisticsApiAggregateResponse statistics = createValidStatisticsResponse();
            List<StatisticsPlayer> players = createInactiveStatisticsPlayers();
            
            ParticipantDto result = StatisticsExtractionHelper.findHighestRatedPlayer(statistics, players);
            
            assertNull(result);
        }
    }

    private MatchDetails createMatchDetailsWithLineup() {
        MatchDetails.Lineup.TeamDetails.Player homePlayer1 = MatchDetails.Lineup.TeamDetails.Player.builder()
                .player(MatchDetails.Lineup.TeamDetails.Player.PlayerDetails.builder()
                        .name("Home Player 1")
                        .position("MIDFIELDER")
                        .active(true)
                        .build())
                .type(MatchDetails.Lineup.TeamDetails.Player.Type.builder()
                        .name("Starting")
                        .build())
                .build();

        MatchDetails.Lineup.TeamDetails.Player homePlayer2 = MatchDetails.Lineup.TeamDetails.Player.builder()
                .player(MatchDetails.Lineup.TeamDetails.Player.PlayerDetails.builder()
                        .name("Home Player 2")
                        .position("FORWARD")
                        .active(true)
                        .build())
                .type(MatchDetails.Lineup.TeamDetails.Player.Type.builder()
                        .name("Starting")
                        .build())
                .build();

        MatchDetails.Lineup.TeamDetails.Player awayPlayer1 = MatchDetails.Lineup.TeamDetails.Player.builder()
                .player(MatchDetails.Lineup.TeamDetails.Player.PlayerDetails.builder()
                        .name("Away Player 1")
                        .position("MIDFIELDER")
                        .active(true)
                        .build())
                .type(MatchDetails.Lineup.TeamDetails.Player.Type.builder()
                        .name("Starting")
                        .build())
                .build();

        MatchDetails.Lineup.TeamDetails.Player awayPlayer2 = MatchDetails.Lineup.TeamDetails.Player.builder()
                .player(MatchDetails.Lineup.TeamDetails.Player.PlayerDetails.builder()
                        .name("Away Player 2")
                        .position("FORWARD")
                        .active(true)
                        .build())
                .type(MatchDetails.Lineup.TeamDetails.Player.Type.builder()
                        .name("Starting")
                        .build())
                .build();

        MatchDetails.Lineup.TeamDetails homeTeamDetails = MatchDetails.Lineup.TeamDetails.builder()
                .players(List.of(homePlayer1, homePlayer2))
                .build();

        MatchDetails.Lineup.TeamDetails awayTeamDetails = MatchDetails.Lineup.TeamDetails.builder()
                .players(List.of(awayPlayer1, awayPlayer2))
                .build();

        MatchDetails.Lineup lineup = MatchDetails.Lineup.builder()
                .homeTeam(homeTeamDetails)
                .awayTeam(awayTeamDetails)
                .build();

        return MatchDetails.builder()
                .homeTeamId("home-team-id")
                .awayTeamId("away-team-id")
                .homeTeam("Home Team")
                .awayTeam("Away Team")
                .lineup(lineup)
                .build();
    }

    private MatchDetails createMatchDetailsWithSquadOnly() {
        MatchDetails.Squad.Player homePlayer1 = MatchDetails.Squad.Player.builder()
                .name("Home Squad Player 1")
                .position("MIDFIELDER")
                .active(true)
                .build();

        MatchDetails.Squad.Player homePlayer2 = MatchDetails.Squad.Player.builder()
                .name("Home Squad Player 2")
                .position("FORWARD")
                .active(true)
                .build();

        MatchDetails.Squad.Player awayPlayer1 = MatchDetails.Squad.Player.builder()
                .name("Away Squad Player 1")
                .position("MIDFIELDER")
                .active(true)
                .build();

        MatchDetails.Squad.Player awayPlayer2 = MatchDetails.Squad.Player.builder()
                .name("Away Squad Player 2")
                .position("FORWARD")
                .active(true)
                .build();

        MatchDetails.Squad.Team homeTeamSquad = MatchDetails.Squad.Team.builder()
                .players(List.of(homePlayer1, homePlayer2))
                .build();

        MatchDetails.Squad.Team awayTeamSquad = MatchDetails.Squad.Team.builder()
                .players(List.of(awayPlayer1, awayPlayer2))
                .build();

        MatchDetails.Squad squad = MatchDetails.Squad.builder()
                .homeTeam(homeTeamSquad)
                .awayTeam(awayTeamSquad)
                .build();

        return MatchDetails.builder()
                .homeTeamId("home-team-id")
                .awayTeamId("away-team-id")
                .homeTeam("Home Team")
                .awayTeam("Away Team")
                .lineup(null)
                .squad(squad)
                .build();
    }

    private StatisticsApiAggregateResponse createValidStatisticsResponse() {
        StatisticDto ratingStatistic = new StatisticDto();
        ratingStatistic.setId(STATISTIC_RATING_AVG_ID);
        ratingStatistic.setName("Rating");
        ratingStatistic.setValue("7.5");

        StatisticDto startsStatistic = new StatisticDto();
        startsStatistic.setId(STATISTIC_STARTED_ID);
        startsStatistic.setName("Events Started");
        startsStatistic.setValue("5");

        ParticipantDto participant = new ParticipantDto();
        participant.setId("player1");
        participant.setName("Player One");
        participant.setEntityType(ENTITY_TYPE_PLAYER);
        participant.setStatistics(List.of(ratingStatistic, startsStatistic));

        StatisticsApiAggregateResponse response = new StatisticsApiAggregateResponse();
        response.setData(List.of(participant));
        return response;
    }

    private StatisticsApiAggregateResponse createStatisticsResponseWithMultiplePlayers() {
        ParticipantDto participant1 = createParticipantOne();
        ParticipantDto participant2 = createParticipantTwo();

        StatisticsApiAggregateResponse response = new StatisticsApiAggregateResponse();
        response.setData(List.of(participant1, participant2));
        return response;
    }

    private StatisticsApiAggregateResponse createStatisticsResponseWithLowStarts() {
        StatisticDto ratingStatistic = new StatisticDto();
        ratingStatistic.setId(STATISTIC_RATING_AVG_ID);
        ratingStatistic.setName("Rating");
        ratingStatistic.setValue("8.0");

        StatisticDto startsStatistic = new StatisticDto();
        startsStatistic.setId(STATISTIC_STARTED_ID);
        startsStatistic.setName("Events Started");
        startsStatistic.setValue("2"); // Below minimum of 3

        ParticipantDto participant = new ParticipantDto();
        participant.setId("player1");
        participant.setName("Player One");
        participant.setEntityType(ENTITY_TYPE_PLAYER);
        participant.setStatistics(List.of(ratingStatistic, startsStatistic));

        StatisticsApiAggregateResponse response = new StatisticsApiAggregateResponse();
        response.setData(List.of(participant));
        return response;
    }

    private List<StatisticsPlayer> createValidStatisticsPlayers() {
        StatisticsPlayer player1 = StatisticsPlayer.builder()
                .name("Player One")
                .lineupStatus("Starting")
                .position("MIDFIELDER")
                .active(true)
                .build();

        StatisticsPlayer player2 = StatisticsPlayer.builder()
                .name("Player Two")
                .lineupStatus("Starting")
                .position("FORWARD")
                .active(true)
                .build();

        return List.of(player1, player2);
    }

    private List<StatisticsPlayer> createInactiveStatisticsPlayers() {
        StatisticsPlayer player1 = StatisticsPlayer.builder()
                .name("Player One")
                .lineupStatus("Injured")
                .position("MIDFIELDER")
                .active(false)
                .build();

        StatisticsPlayer player2 = StatisticsPlayer.builder()
                .name("Player Two")
                .lineupStatus("Suspended")
                .position("FORWARD")
                .active(false)
                .build();

        return List.of(player1, player2);
    }

    @Nested
    @DisplayName("Additional Edge Case Tests")
    class AdditionalEdgeCaseTests {

        @Test
        @DisplayName("Should handle players with invalid rating values")
        void shouldHandlePlayersWithInvalidRatingValues() {
            ParticipantDto participant = createParticipantWithInvalidRating();
            StatisticsApiAggregateResponse statistics = createStatisticsResponse(participant);
            List<StatisticsPlayer> players = createValidStatisticsPlayers();

            ParticipantDto result = StatisticsExtractionHelper.findHighestRatedPlayer(statistics, players);

            // Should still return the player even with invalid rating (defaults to 0.0)
            assertNotNull(result);
            assertEquals("invalid-rating-player", result.getId());
        }

        @Test
        @DisplayName("Should handle players with non-numeric starts values")
        void shouldHandlePlayersWithNonNumericStartsValues() {
            ParticipantDto participant = createParticipantWithInvalidStarts();
            StatisticsApiAggregateResponse statistics = createStatisticsResponse(participant);
            List<StatisticsPlayer> players = createValidStatisticsPlayers();

            ParticipantDto result = StatisticsExtractionHelper.findHighestRatedPlayer(statistics, players);

            // Should return null because starts requirement is not met
            assertNull(result);
        }

        @Test
        @DisplayName("Should handle players with missing statistics")
        void shouldHandlePlayersWithMissingStatistics() {
            ParticipantDto participant = createParticipantWithIdAndName("empty-stats-player", "Player One", Collections.emptyList());
            StatisticsApiAggregateResponse statistics = createStatisticsResponse(participant);
            List<StatisticsPlayer> players = createValidStatisticsPlayers();

            ParticipantDto result = StatisticsExtractionHelper.findHighestRatedPlayer(statistics, players);

            assertNull(result);
        }

        @Test
        @DisplayName("Should handle players with null statistics list")
        void shouldHandlePlayersWithNullStatisticsList() {
            ParticipantDto participant = createParticipantWithIdAndName("null-stats-player", "Player Two", null);
            StatisticsApiAggregateResponse statistics = createStatisticsResponse(participant);
            List<StatisticsPlayer> players = createValidStatisticsPlayers();

            ParticipantDto result = StatisticsExtractionHelper.findHighestRatedPlayer(statistics, players);

            assertNull(result);
        }

        @ParameterizedTest
        @ValueSource(strings = {"", " ", "0", "-1", "2.9"})
        @DisplayName("Should handle boundary values for minimum starts")
        void shouldHandleBoundaryValuesForMinimumStarts(String startsValue) {
            ParticipantDto participant = createParticipantWithCustomStarts(startsValue);
            StatisticsApiAggregateResponse statistics = createStatisticsResponse(participant);
            List<StatisticsPlayer> players = createValidStatisticsPlayers();

            ParticipantDto result = StatisticsExtractionHelper.findHighestRatedPlayer(statistics, players);

            // All these values should result in null because they don't meet minimum starts requirement
            assertNull(result);
        }
    }

    // Helper methods for creating test participants
    private ParticipantDto createParticipantWithInvalidRating() {
        StatisticDto invalidRating = new StatisticDto();
        invalidRating.setId(STATISTIC_RATING_AVG_ID);
        invalidRating.setName("Rating");
        invalidRating.setValue("invalid");

        StatisticDto validStarts = new StatisticDto();
        validStarts.setId(STATISTIC_STARTED_ID);
        validStarts.setName("Events Started");
        validStarts.setValue("5");

        return createParticipantWithIdAndName("invalid-rating-player", "Player One", List.of(invalidRating, validStarts));
    }

    private ParticipantDto createParticipantWithInvalidStarts() {
        StatisticDto validRating = new StatisticDto();
        validRating.setId(STATISTIC_RATING_AVG_ID);
        validRating.setName("Rating");
        validRating.setValue("8.0");

        StatisticDto invalidStarts = new StatisticDto();
        invalidStarts.setId(STATISTIC_STARTED_ID);
        invalidStarts.setName("Events Started");
        invalidStarts.setValue("not-a-number");

        return createParticipantWithIdAndName("invalid-starts-player", "Player Two", List.of(validRating, invalidStarts));
    }

    private ParticipantDto createParticipantWithCustomStarts(String startsValue) {
        StatisticDto validRating = new StatisticDto();
        validRating.setId(STATISTIC_RATING_AVG_ID);
        validRating.setName("Rating");
        validRating.setValue("8.0");

        StatisticDto startsStatistic = new StatisticDto();
        startsStatistic.setId(STATISTIC_STARTED_ID);
        startsStatistic.setName("Events Started");
        startsStatistic.setValue(startsValue);

        // Generate unique ID based on starts value to avoid hardcoding
        String playerId = "player-starts-" + startsValue.replaceAll("[^a-zA-Z0-9]", "");
        String playerName = "Player One"; // Use name that matches lineup

        return createParticipantWithIdAndName(playerId, playerName, List.of(validRating, startsStatistic));
    }

    private ParticipantDto createParticipantOne() {
        // Player 1 - Lower rating but meets requirements
        StatisticDto rating = new StatisticDto();
        rating.setId(STATISTIC_RATING_AVG_ID);
        rating.setName("Rating");
        rating.setValue("7.0");

        StatisticDto starts = new StatisticDto();
        starts.setId(STATISTIC_STARTED_ID);
        starts.setName("Events Started");
        starts.setValue("5");

        return createParticipantWithIdAndName("player1", "Player One", List.of(rating, starts));
    }

    private ParticipantDto createParticipantTwo() {
        // Player 2 - Higher rating and meets requirements
        StatisticDto rating = new StatisticDto();
        rating.setId(STATISTIC_RATING_AVG_ID);
        rating.setName("Rating");
        rating.setValue("8.5");

        StatisticDto starts = new StatisticDto();
        starts.setId(STATISTIC_STARTED_ID);
        starts.setName("Events Started");
        starts.setValue("7");

        return createParticipantWithIdAndName("player2", "Player Two", List.of(rating, starts));
    }

    private ParticipantDto createParticipant(List<StatisticDto> statistics) {
        return createParticipantWithIdAndName("player1", "Player One", statistics);
    }

    private ParticipantDto createParticipantWithIdAndName(String id, String name, List<StatisticDto> statistics) {
        ParticipantDto participant = new ParticipantDto();
        participant.setId(id);
        participant.setName(name);
        participant.setEntityType(ENTITY_TYPE_PLAYER);
        participant.setStatistics(statistics);
        return participant;
    }

    private StatisticsApiAggregateResponse createStatisticsResponse(ParticipantDto participant) {
        StatisticsApiAggregateResponse statistics = new StatisticsApiAggregateResponse();
        statistics.setData(List.of(participant));
        return statistics;
    }
}
